using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages bulk sales events with special customer groups
    /// </summary>
    public class BulkSalesManager : MonoBehaviour
    {
        [Header("Bulk Sales Settings")]
        public float eventFrequency = 0.1f; // Chance per hour
        public float eventDuration = 3600f; // 1 hour in seconds
        
        // Active bulk sales events
        private List<BulkSalesEvent> activeEvents = new List<BulkSalesEvent>();
        private List<BulkCustomer> availableCustomers = new List<BulkCustomer>();
        
        // Events
        public System.Action<BulkSalesEvent> OnBulkSalesEventStarted;
        public System.Action<BulkSalesEvent> OnBulkSalesEventEnded;
        public System.Action<BulkSale> OnBulkSaleCompleted;
        
        private void Start()
        {
            InitializeBulkCustomers();
        }
        
        private void Update()
        {
            UpdateBulkSalesEvents();
            CheckForNewEvents();
        }
        
        private void InitializeBulkCustomers()
        {
            // Biker Gang
            availableCustomers.Add(new BulkCustomer
            {
                name = "Iron Wolves MC",
                type = CustomerType.BikerGang,
                preferredDrug = DrugType.Methamphetamine,
                minQuantity = 50,
                maxQuantity = 200,
                priceMultiplier = 1.2f,
                riskLevel = 0.3f,
                loyaltyLevel = 0.6f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 30))
            });
            
            // Business People
            availableCustomers.Add(new BulkCustomer
            {
                name = "Corporate Executive Group",
                type = CustomerType.BusinessPeople,
                preferredDrug = DrugType.Cocaine,
                minQuantity = 20,
                maxQuantity = 100,
                priceMultiplier = 1.5f,
                riskLevel = 0.2f,
                loyaltyLevel = 0.8f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 14))
            });
            
            // College Students
            availableCustomers.Add(new BulkCustomer
            {
                name = "University Party Network",
                type = CustomerType.College,
                preferredDrug = DrugType.MDMA,
                minQuantity = 30,
                maxQuantity = 150,
                priceMultiplier = 0.9f,
                riskLevel = 0.4f,
                loyaltyLevel = 0.4f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 7))
            });
            
            // Tourists
            availableCustomers.Add(new BulkCustomer
            {
                name = "International Tourist Group",
                type = CustomerType.Tourists,
                preferredDrug = DrugType.Cannabis,
                minQuantity = 10,
                maxQuantity = 80,
                priceMultiplier = 1.3f,
                riskLevel = 0.6f,
                loyaltyLevel = 0.2f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 60))
            });
            
            // Local Dealers
            availableCustomers.Add(new BulkCustomer
            {
                name = "Street Dealer Network",
                type = CustomerType.LocalDealers,
                preferredDrug = DrugType.Heroin,
                minQuantity = 25,
                maxQuantity = 120,
                priceMultiplier = 0.8f,
                riskLevel = 0.5f,
                loyaltyLevel = 0.7f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 21))
            });
            
            // Night Club Owners
            availableCustomers.Add(new BulkCustomer
            {
                name = "Club Owner Consortium",
                type = CustomerType.NightClubOwners,
                preferredDrug = DrugType.MDMA,
                minQuantity = 40,
                maxQuantity = 180,
                priceMultiplier = 1.1f,
                riskLevel = 0.3f,
                loyaltyLevel = 0.6f,
                lastVisit = System.DateTime.Now.AddDays(-Random.Range(1, 14))
            });
        }
        
        private void UpdateBulkSalesEvents()
        {
            for (int i = activeEvents.Count - 1; i >= 0; i--)
            {
                var salesEvent = activeEvents[i];
                
                if (Time.time - salesEvent.startTime >= salesEvent.duration)
                {
                    EndBulkSalesEvent(salesEvent);
                    activeEvents.RemoveAt(i);
                }
            }
        }
        
        private void CheckForNewEvents()
        {
            // Check for new events every hour
            if (Random.value < eventFrequency * Time.deltaTime / 3600f)
            {
                GenerateBulkSalesEvent();
            }
        }
        
        private void GenerateBulkSalesEvent()
        {
            // Select available customers
            var availableCustomersForEvent = availableCustomers.Where(c => c.IsAvailable()).ToList();
            
            if (availableCustomersForEvent.Count == 0) return;
            
            var customer = availableCustomersForEvent[Random.Range(0, availableCustomersForEvent.Count)];
            
            var salesEvent = new BulkSalesEvent
            {
                id = System.Guid.NewGuid().ToString(),
                customer = customer,
                startTime = Time.time,
                duration = eventDuration + Random.Range(-600f, 600f), // ±10 minutes variance
                isActive = true,
                location = GenerateEventLocation(),
                specialRequirements = GenerateSpecialRequirements(customer.type)
            };
            
            activeEvents.Add(salesEvent);
            OnBulkSalesEventStarted?.Invoke(salesEvent);
            
            Debug.Log($"Bulk sales event started: {customer.name} wants {customer.preferredDrug}");
        }
        
        private Vector3 GenerateEventLocation()
        {
            // Generate random location within city bounds
            return new Vector3(
                Random.Range(-500f, 500f),
                0f,
                Random.Range(-500f, 500f)
            );
        }
        
        private List<string> GenerateSpecialRequirements(CustomerType customerType)
        {
            var requirements = new List<string>();
            
            switch (customerType)
            {
                case CustomerType.BikerGang:
                    requirements.Add("High purity required");
                    requirements.Add("Cash only transaction");
                    break;
                    
                case CustomerType.BusinessPeople:
                    requirements.Add("Discrete packaging");
                    requirements.Add("Premium quality only");
                    requirements.Add("Professional meeting location");
                    break;
                    
                case CustomerType.College:
                    requirements.Add("Party-friendly packaging");
                    requirements.Add("Bulk discount expected");
                    break;
                    
                case CustomerType.Tourists:
                    requirements.Add("Easy to transport");
                    requirements.Add("No local heat generation");
                    break;
                    
                case CustomerType.LocalDealers:
                    requirements.Add("Resale packaging");
                    requirements.Add("Competitive pricing");
                    break;
                    
                case CustomerType.NightClubOwners:
                    requirements.Add("Club-appropriate substances");
                    requirements.Add("Regular supply arrangement");
                    break;
            }
            
            return requirements;
        }
        
        public bool AcceptBulkSale(string eventId)
        {
            var salesEvent = activeEvents.FirstOrDefault(e => e.id == eventId);
            if (salesEvent == null || !salesEvent.isActive)
            {
                return false;
            }
            
            var customer = salesEvent.customer;
            var quantity = customer.GetDesiredQuantity();
            
            // Check if player has enough drugs
            var drugManager = GameManager.Instance.drugManager;
            if (drugManager.GetTotalQuantity(customer.preferredDrug) < quantity)
            {
                Debug.Log("Insufficient drugs for bulk sale");
                return false;
            }
            
            // Calculate sale details
            var basePrice = DrugDatabase.GetBasePrice(customer.preferredDrug);
            var totalPrice = basePrice * quantity * customer.priceMultiplier;
            
            // Process the sale
            if (drugManager.SellDrugs(customer.preferredDrug, quantity))
            {
                var bulkSale = new BulkSale
                {
                    customer = customer,
                    drugType = customer.preferredDrug,
                    quantity = quantity,
                    pricePerUnit = basePrice * customer.priceMultiplier,
                    totalPrice = totalPrice,
                    timestamp = Time.time,
                    location = salesEvent.location
                };
                
                // Add money and heat
                GameManager.Instance.moneyManager.AddDirtyMoney(totalPrice, $"Bulk sale to {customer.name}");
                GameManager.Instance.policeManager.AddHeat(quantity * 0.1f, "Bulk drug sale");
                
                // Update customer loyalty and last visit
                customer.loyaltyLevel = Mathf.Min(1f, customer.loyaltyLevel + 0.1f);
                customer.lastVisit = System.DateTime.Now;
                customer.totalPurchases++;
                
                OnBulkSaleCompleted?.Invoke(bulkSale);
                EndBulkSalesEvent(salesEvent);
                activeEvents.Remove(salesEvent);
                
                Debug.Log($"Bulk sale completed: {quantity} {customer.preferredDrug} for ${totalPrice:F0}");
                return true;
            }
            
            return false;
        }
        
        public bool RejectBulkSale(string eventId)
        {
            var salesEvent = activeEvents.FirstOrDefault(e => e.id == eventId);
            if (salesEvent == null)
            {
                return false;
            }
            
            // Reduce customer loyalty
            salesEvent.customer.loyaltyLevel = Mathf.Max(0f, salesEvent.customer.loyaltyLevel - 0.2f);
            
            EndBulkSalesEvent(salesEvent);
            activeEvents.Remove(salesEvent);
            
            Debug.Log($"Rejected bulk sale from {salesEvent.customer.name}");
            return true;
        }
        
        private void EndBulkSalesEvent(BulkSalesEvent salesEvent)
        {
            salesEvent.isActive = false;
            OnBulkSalesEventEnded?.Invoke(salesEvent);
        }
        
        public List<BulkSalesEvent> GetActiveEvents()
        {
            return new List<BulkSalesEvent>(activeEvents);
        }
        
        public List<BulkCustomer> GetCustomerHistory()
        {
            return new List<BulkCustomer>(availableCustomers);
        }
    }
    
    [System.Serializable]
    public class BulkSalesEvent
    {
        public string id;
        public BulkCustomer customer;
        public float startTime;
        public float duration;
        public bool isActive;
        public Vector3 location;
        public List<string> specialRequirements;
        
        public float GetRemainingTime()
        {
            return Mathf.Max(0f, duration - (Time.time - startTime));
        }
        
        public float GetProgress()
        {
            return Mathf.Clamp01((Time.time - startTime) / duration);
        }
    }
    
    [System.Serializable]
    public class BulkSale
    {
        public BulkCustomer customer;
        public DrugType drugType;
        public int quantity;
        public float pricePerUnit;
        public float totalPrice;
        public float timestamp;
        public Vector3 location;
        
        public float GetProfit()
        {
            var baseCost = DrugDatabase.GetBasePrice(drugType) * 0.3f; // Assume 30% cost
            return totalPrice - (baseCost * quantity);
        }
    }
}
