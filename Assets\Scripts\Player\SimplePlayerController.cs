using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Simple player controller that works without errors
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    public class SimplePlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        public float walkSpeed = 5f;
        public float runSpeed = 10f;
        public float jumpHeight = 2f;
        public float gravity = -9.81f;
        
        [Header("Input Settings")]
        public KeyCode runKey = KeyCode.LeftShift;
        public KeyCode jumpKey = KeyCode.Space;
        public KeyCode interactKey = KeyCode.E;
        
        private CharacterController controller;
        private Vector3 velocity;
        private bool isGrounded;
        private bool isRunning;
        
        private void Start()
        {
            controller = GetComponent<CharacterController>();
            
            // Set player tag for identification
            gameObject.tag = "Player";
            
            Debug.Log("Simple Player Controller initialized. Use WASD to move, Shift to run, Space to jump.");
        }
        
        private void Update()
        {
            HandleInput();
            HandleMovement();
        }
        
        private void HandleInput()
        {
            isRunning = Input.GetKey(runKey);
            
            if (Input.GetKeyDown(jumpKey) && isGrounded)
            {
                Jump();
            }
            
            if (Input.GetKeyDown(interactKey))
            {
                TryInteract();
            }
        }
        
        private void HandleMovement()
        {
            // Ground check
            isGrounded = controller.isGrounded;
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f; // Small downward force to keep grounded
            }
            
            // Get input
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            // Calculate movement direction
            Vector3 direction = new Vector3(horizontal, 0f, vertical).normalized;
            
            if (direction.magnitude >= 0.1f)
            {
                // Calculate target angle based on camera
                Camera mainCamera = Camera.main;
                if (mainCamera != null)
                {
                    float targetAngle = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg + mainCamera.transform.eulerAngles.y;
                    float angle = Mathf.LerpAngle(transform.eulerAngles.y, targetAngle, Time.deltaTime * 10f);
                    transform.rotation = Quaternion.AngleAxis(angle, Vector3.up);
                    
                    Vector3 moveDirection = Quaternion.AngleAxis(targetAngle, Vector3.up) * Vector3.forward;
                    float currentSpeed = isRunning ? runSpeed : walkSpeed;
                    controller.Move(moveDirection.normalized * currentSpeed * Time.deltaTime);
                }
            }
            
            // Apply gravity
            velocity.y += gravity * Time.deltaTime;
            controller.Move(velocity * Time.deltaTime);
        }
        
        private void Jump()
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            Debug.Log("Player jumped!");
        }
        
        private void TryInteract()
        {
            Debug.Log("Player tried to interact (E key pressed)");
            
            // Simple interaction test
            var gameManager = SimpleGameManager.Instance;
            if (gameManager != null)
            {
                // Simulate drug dealing
                gameManager.SellDrugs(Random.Range(50f, 200f));
            }
        }
        
        private void OnControllerColliderHit(ControllerColliderHit hit)
        {
            // Handle collision feedback
            if (hit.gameObject.name.Contains("Ground"))
            {
                // Player is on ground
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw movement indicators in editor
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(transform.position, 1f);
            
            if (isRunning)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position + Vector3.up, 0.5f);
            }
        }
    }
}
