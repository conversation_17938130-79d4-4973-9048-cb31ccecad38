%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!129 &1
PlayerSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 23
  productGUID: 12345678901234567890123456789000
  AndroidProfiler: 0
  AndroidFilterTouchesWhenObscured: 0
  AndroidEnableSustainedPerformanceMode: 0
  defaultScreenOrientation: 4
  targetDevice: 2
  useOnDemandResources: 0
  accelerometerFrequency: 60
  companyName: Street Empire Studios
  productName: Street Empire
  defaultCursor: {fileID: 0}
  cursorHotspot: {x: 0, y: 0}
  m_SplashScreenBackgroundColor: {r: 0.13725491, g: 0.12156863, b: 0.1254902, a: 1}
  m_ShowUnitySplashScreen: 1
  m_ShowUnitySplashLogo: 1
  m_SplashScreenOverlayOpacity: 1
  m_SplashScreenAnimation: 1
  m_SplashScreenLogoStyle: 1
  m_SplashScreenDrawMode: 0
  m_SplashScreenBackgroundAnimationZoom: 1
  m_SplashScreenLogoAnimationZoom: 1
  m_SplashScreenBackgroundLandscapeAspect: 1
  m_SplashScreenBackgroundPortraitAspect: 1
  m_SplashScreenBackgroundLandscapeUvs:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  m_SplashScreenBackgroundPortraitUvs:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  m_SplashScreenLogos: []
  m_VirtualRealitySDKs: []
  m_TargetPixelDensity: 30
  m_IOSHighResolutionPortraitSplashScreen: {fileID: 0}
  m_IOSHighResolutionLandscapeSplashScreen: {fileID: 0}
  m_IOSLaunchScreenType: 0
  m_IOSLaunchScreenPortrait: {fileID: 0}
  m_IOSLaunchScreenLandscape: {fileID: 0}
  m_IOSLaunchScreenBackgroundColor:
    serializedVersion: 2
    rgba: 0
  m_IOSLaunchScreenFillPct: 100
  m_IOSLaunchScreenSize: 100
  m_IOSLaunchScreenCustomXibPath:
  m_IOSLaunchScreeniPadType: 0
  m_IOSLaunchScreeniPadImage: {fileID: 0}
  m_IOSLaunchScreeniPadBackgroundColor:
    serializedVersion: 2
    rgba: 0
  m_IOSLaunchScreeniPadFillPct: 100
  m_IOSLaunchScreeniPadSize: 100
  m_IOSLaunchScreeniPadCustomXibPath:
  m_IOSUseLaunchScreenStoryboard: 0
  m_IOSLaunchScreenCustomStoryboardPath:
  m_IOSDeviceRequirements: []
  m_IOSURLSchemes: []
  m_IOSBackgroundModes: 0
  m_IOSMetalForceHardShadows: 0
  m_ODRMaxTextureSize: 1024
  m_IOSAppleDeveloperTeamID:
  m_IOSManualSigningProvisioningProfileID:
  m_IOSManualSigningProvisioningProfileType: 0
  m_appleEnableAutomaticSigning: 0
  m_iOSRequireARKit: 0
  m_iOSAutomaticallyDetectAndAddCapabilities: 1
  m_appleEnableProMotion: 0
  m_shaderPrecisionModel: 0
  m_vulkanNumSwapchainBuffers: 3
  m_vulkanEnableSetSRGBWrite: 0
  m_vulkanEnablePreTransform: 0
  m_vulkanEnableLateAcquireNextImage: 0
  m_vulkanEnableCommandBufferRecycling: 1
  m_supportedAspectRatios:
    4:3: 1
    5:4: 1
    16:10: 1
    16:9: 1
    Others: 1
  bundleVersion: 1.0.0
  preloadedAssets: []
  metroInputSource: 0
  wsaTransparentSwapchain: 0
  m_HolographicPauseOnTrackingLoss: 1
  xboxOneDisableKinectGpuReservation: 1
  xboxOneEnable7thCore: 1
  vrSettings:
    cardboard:
      depthFormat: 0
      enableTransitionView: 0
    daydream:
      depthFormat: 0
      useSustainedPerformanceMode: 0
      enableVideoLayer: 0
      useProtectedVideoMemory: 0
      minimumSupportedHeadTracking: 0
      maximumSupportedHeadTracking: 1
    hololens:
      depthFormat: 1
      depthBufferSharingEnabled: 1
    lumin:
      depthFormat: 0
      frameTiming: 2
      enableGLCache: 0
      glCacheMaxBlobSize: 524288
      glCacheMaxFileSize: 8388608
    oculus:
      sharedDepthBuffer: 1
      dashSupport: 1
      lowOverheadMode: 0
      protectedContext: 0
      v2Signing: 1
    enable360StereoCapture: 0
  isWsaHolographicRemotingEnabled: 0
  enableFrameTimingStats: 0
  useHDRDisplay: 0
  D3DHDRBitDepth: 0
  m_ColorGamuts: 00000000
  targetPixelDensity: 30
  resolutionScalingMode: 0
  androidSupportedAspectRatio: 1
  androidMaxAspectRatio: 2.1
  applicationIdentifier:
    Android: com.streetempirestudios.streetempire
    Standalone: com.streetempirestudios.streetempire
  buildNumber:
    Standalone: 0
    iPhone: 0
    tvOS: 0
  overrideDefaultApplicationIdentifier: 0
  AndroidBundleVersionCode: 1
  AndroidMinSdkVersion: 22
  AndroidTargetSdkVersion: 0
  AndroidPreferredInstallLocation: 1
  aotOptions:
  stripEngineCode: 1
  iPhoneStrippingLevel: 0
  iPhoneScriptCallOptimization: 0
  ForceInternetPermission: 0
  ForceSDCardPermission: 0
  CreateWallpaper: 0
  APKExpansionFiles: 0
  keepLoadedShadersAlive: 0
  StripUnusedMeshComponents: 1
  VertexChannelCompressionMask: 4054
  iPhoneSdkVersion: 988
  iOSTargetOSVersionString: 11.0
  tvOSSdkVersion: 0
  tvOSRequireExtendedGameController: 0
  tvOSTargetOSVersionString: 11.0
  uIPrerenderedIcon: 0
  uIRequiresPersistentWiFi: 0
  uIRequiresFullScreen: 1
  uIStatusBarHidden: 1
  uIExitOnSuspend: 0
  uIStatusBarStyle: 0
  appleTVSplashScreen: {fileID: 0}
  appleTVSplashScreen2x: {fileID: 0}
  tvOSSmallIconLayers: []
  tvOSSmallIconLayers2x: []
  tvOSLargeIconLayers: []
  tvOSLargeIconLayers2x: []
  tvOSTopShelfImageLayers: []
  tvOSTopShelfImageLayers2x: []
  tvOSTopShelfImageWideLayers: []
  tvOSTopShelfImageWideLayers2x: []
  iOSLaunchScreenType: 0
  iOSLaunchScreenPortrait: {fileID: 0}
  iOSLaunchScreenLandscape: {fileID: 0}
  iOSLaunchScreenBackgroundColor:
    serializedVersion: 2
    rgba: 0
  iOSLaunchScreenFillPct: 100
  iOSLaunchScreenSize: 100
  iOSLaunchScreenCustomXibPath:
  iOSLaunchScreeniPadType: 0
  iOSLaunchScreeniPadImage: {fileID: 0}
  iOSLaunchScreeniPadBackgroundColor:
    serializedVersion: 2
    rgba: 0
  iOSLaunchScreeniPadFillPct: 100
  iOSLaunchScreeniPadSize: 100
  iOSLaunchScreeniPadCustomXibPath:
  iOSUseLaunchScreenStoryboard: 0
  iOSLaunchScreenCustomStoryboardPath:
  iOSDeviceRequirements: []
  iOSURLSchemes: []
  iOSBackgroundModes: 0
  iOSMetalForceHardShadows: 0
  appleDeveloperTeamID:
  iOSManualSigningProvisioningProfileID:
  tvOSManualSigningProvisioningProfileID:
  iOSManualSigningProvisioningProfileType: 0
  tvOSManualSigningProvisioningProfileType: 0
  appleEnableAutomaticSigning: 0
  iOSRequireARKit: 0
  iOSAutomaticallyDetectAndAddCapabilities: 1
  appleEnableProMotion: 0
  clonedFromGUID: c0afd0d1d80e3634a9dac47e8a0426ea
  templatePackageId: com.unity.template.3d@5.0.4
  templateDefaultScene: Assets/Scenes/SampleScene.unity
  AndroidTargetArchitectures: 1
  AndroidSplashScreenScale: 0
  androidSplashScreen: {fileID: 0}
  AndroidKeystoreName:
  AndroidKeyaliasName:
  AndroidBuildApkPerCpuArchitecture: 0
  AndroidTVCompatibility: 0
  AndroidIsGame: 1
  AndroidEnableTango: 0
  androidEnableBanner: 1
  androidUseLowAccuracyLocation: 0
  androidUseCustomKeystore: 0
  m_AndroidBanners:
  - width: 320
    height: 180
    banner: {fileID: 0}
  androidGamepadSupportLevel: 0
  AndroidValidateAppBundleSize: 1
  AndroidAppBundleSizeToValidate: 150
  m_BuildTargetIcons: []
  m_BuildTargetPlatformIcons: []
  m_BuildTargetBatching:
  - m_BuildTarget: Standalone
    m_StaticBatching: 1
    m_DynamicBatching: 0
  - m_BuildTarget: tvOS
    m_StaticBatching: 1
    m_DynamicBatching: 0
  - m_BuildTarget: Android
    m_StaticBatching: 1
    m_DynamicBatching: 0
  - m_BuildTarget: iPhone
    m_StaticBatching: 1
    m_DynamicBatching: 0
  - m_BuildTarget: WebGL
    m_StaticBatching: 0
    m_DynamicBatching: 0
  m_BuildTargetGraphicsJobs:
  - m_BuildTarget: MacStandaloneSupport
    m_GraphicsJobs: 0
  - m_BuildTarget: Switch
    m_GraphicsJobs: 1
  - m_BuildTarget: MetroSupport
    m_GraphicsJobs: 1
  - m_BuildTarget: AppleTVSupport
    m_GraphicsJobs: 0
  - m_BuildTarget: BJMSupport
    m_GraphicsJobs: 1
  - m_BuildTarget: LinuxStandaloneSupport
    m_GraphicsJobs: 1
  - m_BuildTarget: PS4Player
    m_GraphicsJobs: 1
  - m_BuildTarget: iOSSupport
    m_GraphicsJobs: 0
  - m_BuildTarget: WindowsStandaloneSupport
    m_GraphicsJobs: 1
  - m_BuildTarget: XboxOnePlayer
    m_GraphicsJobs: 1
  - m_BuildTarget: LuminSupport
    m_GraphicsJobs: 0
  - m_BuildTarget: AndroidPlayer
    m_GraphicsJobs: 0
  - m_BuildTarget: WebGLSupport
    m_GraphicsJobs: 0
  m_BuildTargetGraphicsJobMode:
  - m_BuildTarget: PS4Player
    m_GraphicsJobMode: 0
  - m_BuildTarget: XboxOnePlayer
    m_GraphicsJobMode: 0
  m_BuildTargetGraphicsAPIs:
  - m_BuildTarget: AndroidPlayer
    m_APIs: 150000000b000000
    m_Automatic: 0
  - m_BuildTarget: iOSSupport
    m_APIs: 10000000
    m_Automatic: 1
  - m_BuildTarget: AppleTVSupport
    m_APIs: 10000000
    m_Automatic: 0
  - m_BuildTarget: WebGLSupport
    m_APIs: 0b000000
    m_Automatic: 1
  m_BuildTargetVRSettings:
  - m_BuildTarget: Standalone
    m_Enabled: 0
    m_Devices:
    - Oculus
    - OpenVR
  openGLRequireES31: 0
  openGLRequireES31AEP: 0
  openGLRequireES32: 0
  m_TemplateCustomTags: {}
  mobileMTRendering:
    Android: 1
    iPhone: 1
    tvOS: 1
  m_BuildTargetGroupLightmapEncodingQuality:
  - m_BuildTarget: Android
    m_EncodingQuality: 1
  - m_BuildTarget: iPhone
    m_EncodingQuality: 1
  - m_BuildTarget: tvOS
    m_EncodingQuality: 1
  m_BuildTargetGroupLightmapSettings: []
  playModeTestRunnerEnabled: 0
  runPlayModeTestAsEditModeTest: 0
  actionOnDotNetUnhandledException: 1
  enableInternalProfiler: 0
  logObjCUncaughtExceptions: 1
  enableCrashReportAPI: 0
  cameraUsageDescription:
  locationUsageDescription:
  microphoneUsageDescription:
  switchScreenResolutionBehavior: 2
  switchNMETAOverride:
  switchNetLibKey:
  switchSocketMemoryPoolSize: 6144
  switchSocketAllocatorPoolSize: 128
  switchSocketConcurrencyLimit: 14
  switchSocketBufferEfficiency: 4
  switchStackSize: 131072
  switchNonVolatileMemorySize: 0
  switchIsHoldTypeHorizontal: 0
  switchSupportedNpadStyles: 22
  switchNativeFsCacheSize: 32
  switchIsHoldTypeHorizontal: 0
  switchNpadCount: 8
  switchSocketConfigEnabled: 0
  switchTcpInitialSendBufferSize: 32
  switchTcpInitialReceiveBufferSize: 64
  switchTcpAutoSendBufferSizeMax: 256
  switchTcpAutoReceiveBufferSizeMax: 256
  switchUdpSendBufferSize: 9
  switchUdpReceiveBufferSize: 42
  switchSocketBufferEfficiency: 4
  switchSocketInitializeEnabled: 1
  switchNetworkInterfaceManagerInitializeEnabled: 1
  switchPlayerConnectionEnabled: 1
  switchUseNewStyleFilepaths: 0
  switchUseMicroSleepForYield: 1
  switchEnableRamDiskSupport: 0
  switchMicroSleepForYieldTime: 25
  switchRamDiskSpaceSize: 12
  ps4NPAgeRating: 12
  ps4NPTitleSecret:
  ps4NPTrophyPackPath:
  ps4ParentalLevel: 11
  ps4ContentID: ED1633-NPXX51362_00-0000000000000000
  ps4Category: 0
  ps4MasterVersion: 01.00
  ps4AppVersion: 01.00
  ps4AppType: 0
  ps4ParamSfxPath:
  ps4VideoOutPixelFormat: 0
  ps4VideoOutInitialWidth: 1920
  ps4VideoOutBaseModeInitialWidth: 1920
  ps4VideoOutReprojectionRate: 60
  ps4PronunciationXMLPath:
  ps4PronunciationSIGPath:
  ps4BackgroundImagePath:
  ps4StartupImagePath:
  ps4StartupImagesFolder:
  ps4IconImagesFolder:
  ps4SaveDataImagePath:
  ps4SdkOverride:
  ps4BGMPath:
  ps4ShareFilePath:
  ps4ShareOverlayImagePath:
  ps4PrivacyGuardImagePath:
  ps4ExtraSceSysFile:
  ps4NPtitleDatPath:
  ps4RemotePlayKeyAssignment: -1
  ps4RemotePlayKeyMappingDir:
  ps4PlayTogetherPlayerCount: 0
  ps4EnterButtonAssignment: 1
  ps4ApplicationParam1: 0
  ps4ApplicationParam2: 0
  ps4ApplicationParam3: 0
  ps4ApplicationParam4: 0
  ps4DownloadDataSize: 0
  ps4GarlicHeapSize: 2048
  ps4ProGarlicHeapSize: 2560
  playerPrefsMaxSize: 32768
  ps4Passcode: frAQBc8Wsa1xVPfvJcrgRYwTiizs2trQ
  ps4pnSessions: 1
  ps4pnPresence: 1
  ps4pnFriends: 1
  ps4pnGameCustomData: 1
  playerPrefsSupport: 0
  enableApplicationExit: 0
  resetTempFolder: 1
  restrictedAudioUsageRights: 0
  ps4UseResolutionFallback: 0
  ps4ReprojectionSupport: 0
  ps4UseAudio3dBackend: 0
  ps4UseLowGarlicHeapForSocialScreen: 0
  ps4SocialScreenEnabled: 0
  ps4ScriptOptimizationLevel: 0
  ps4Audio3dVirtualSpeakerCount: 14
  ps4attribCpuUsage: 0
  ps4PatchPkgPath:
  ps4PatchLatestPkgPath:
  ps4PatchChangeinfoPath:
  ps4PatchDayOne: 0
  ps4attribUserManagement: 0
  ps4attribMoveSupport: 0
  ps4attrib3DSupport: 0
  ps4attribShareSupport: 0
  ps4attribExclusiveVR: 0
  ps4disableAutoHideSplash: 0
  ps4videoRecordingFeaturesUsed: 0
  ps4contentSearchFeaturesUsed: 0
  ps4CompatibilityPS5: 0
  ps4GPU800MHz: 1
  ps4attribEyeToEyeDistanceSettingVR: 0
  ps4IncludedModules: []
  ps4attribVROutputEnabled: 0
  monoEnv:
  splashScreenBackgroundSourceLandscape: {fileID: 0}
  splashScreenBackgroundSourcePortrait: {fileID: 0}
  blurSplashScreenBackground: 1
  spritePackerPolicy:
  webGLMemorySize: 16
  webGLExceptionSupport: 1
  webGLNameFilesAsHashes: 0
  webGLDataCaching: 1
  webGLDebugSymbols: 0
  webGLEmscriptenArgs:
  webGLModulesDirectory:
  webGLTemplate: APPLICATION:Default
  webGLAnalyzeBuildSize: 0
  webGLUseEmbeddedResources: 0
  webGLCompressionFormat: 1
  webGLLinkerTarget: 1
  webGLThreadsSupport: 0
  webGLWasmStreaming: 0
  scriptingDefineSymbols:
    1: STREET_EMPIRE_DEBUG
    7: STREET_EMPIRE_DEBUG
    13: STREET_EMPIRE_DEBUG
    19: STREET_EMPIRE_DEBUG
    21: STREET_EMPIRE_DEBUG
    25: STREET_EMPIRE_DEBUG
    27: STREET_EMPIRE_DEBUG
    28: STREET_EMPIRE_DEBUG
    29: STREET_EMPIRE_DEBUG
    30: STREET_EMPIRE_DEBUG
  platformArchitecture: {}
  scriptingBackend: {}
  il2cppCompilerConfiguration: {}
  managedStrippingLevel: {}
  incrementalIl2cppBuild: {}
  allowUnsafeCode: 0
  useDeterministicCompilation: 1
  enableRoslynAnalyzers: 1
  additionalIl2CppArgs:
  scriptingRuntimeVersion: 1
  gcIncremental: 0
  assemblyVersionValidation: 1
  gcWBarrierValidation: 0
  apiCompatibilityLevelPerPlatform: {}
  m_RenderingPath: 1
  m_MobileRenderingPath: 1
  metroPackageName: Street Empire
  metroPackageVersion:
  metroCertificatePath:
  metroCertificatePassword:
  metroCertificateSubject:
  metroCertificateIssuer:
  metroCertificateNotAfter: 0000000000000000
  metroApplicationDescription: Street Empire - Drug Dealing Simulation
  wsaImages: {}
  metroTileShortName:
  metroTileShowName: 0
  metroMediumTileShowName: 0
  metroLargeTileShowName: 0
  metroWideTileShowName: 0
  metroSupportStreamingInstall: 0
  metroLastRequiredScene: 0
  metroDefaultTileSize: 1
  metroTileForegroundText: 2
  metroTileBackgroundColor: {r: 0.13333334, g: 0.17254902, b: 0.21568628, a: 0}
  metroSplashScreenBackgroundColor: {r: 0.12941177, g: 0.17254902, b: 0.21568628, a: 1}
  metroSplashScreenUseBackgroundColor: 0
  platformCapabilities: {}
  metroTargetDeviceFamily: {}
  metroFTAName:
  metroFTAFileTypes: []
  metroProtocolName:
  XboxOneProductId:
  XboxOneUpdateKey:
  XboxOneSandboxId:
  XboxOneContentId:
  XboxOneTitleId:
  XboxOneSCId:
  XboxOneGameOsOverridePath:
  XboxOnePackagingOverridePath:
  XboxOneAppManifestOverridePath:
  XboxOneVersion: *******
  XboxOnePackageEncryption: 0
  XboxOnePackageUpdateGranularity: 2
  XboxOneDescription:
  XboxOneLanguage:
  - enus
  XboxOneCapability: []
  XboxOneGameRating: {}
  XboxOneIsContentPackage: 0
  XboxOneEnhancedXboxCompatibilityMode: 0
  XboxOneEnableGPUVariability: 1
  XboxOneSockets: {}
  XboxOneSplashScreen: {fileID: 0}
  XboxOneAllowedProductIds: []
  XboxOnePersistentLocalStorageSize: 0
  XboxOneXTitleMemory: 8
  XboxOneOverrideIdentityName:
  XboxOneOverrideIdentityPublisher:
  vrEditorSettings: {}
  cloudServicesEnabled:
    UNet: 1
  luminIcon:
    m_Name:
    m_ModelFolderPath:
    m_PortalFolderPath:
  luminCert:
    m_CertPath:
    m_SignPackage: 1
  luminIsChannelApp: 0
  luminVersion:
    m_VersionCode: 1
    m_VersionName:
  apiCompatibilityLevel: 6
  cloudProjectId:
  framebufferDepthMemorylessMode: 0
  qualitySettingsNames: []
  projectName:
  organizationId:
  cloudEnabled: 0
  enableNativePlatformBackendsForNewInputSystem: 0
  disableOldInputManagerSupport: 0
  legacyClampBlendShapeWeights: 0
  virtualTexturingSupportEnabled: 0