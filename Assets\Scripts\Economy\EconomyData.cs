using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Data structures for economy system
    /// </summary>
    
    [System.Serializable]
    public class Transaction
    {
        public TransactionType type;
        public float amount;
        public string description;
        public bool isCleanMoney;
        public System.DateTime timestamp;
    }
    
    [System.Serializable]
    public class Business
    {
        public BusinessType type;
        public string name;
        public float purchasePrice;
        public float dailyIncome;
        public float launderingCapacity;
        public System.DateTime purchaseDate;
        public float lastIncomeGenerated;
        
        public float GetROI()
        {
            var daysSincePurchase = (System.DateTime.Now - purchaseDate).Days;
            if (daysSincePurchase <= 0) return 0f;
            
            var totalIncome = dailyIncome * daysSincePurchase;
            return (totalIncome / purchasePrice) * 100f;
        }
    }
    
    [System.Serializable]
    public class BusinessData
    {
        public string name;
        public float purchasePrice;
        public float dailyIncome;
        public float launderingCapacity;
        
        public BusinessData(string name, float price, float income, float capacity)
        {
            this.name = name;
            this.purchasePrice = price;
            this.dailyIncome = income;
            this.launderingCapacity = capacity;
        }
    }
    
    public enum TransactionType
    {
        Income,
        Expense,
        Laundering,
        Investment,
        Theft,
        Fine,
        Bribe
    }
    
    public enum BusinessType
    {
        CarWash,
        Restaurant,
        LaundryMat,
        NightClub,
        GasStation,
        PawnShop,
        TattooShop,
        BarberShop
    }
    
    /// <summary>
    /// Market data for dynamic pricing
    /// </summary>
    [System.Serializable]
    public class MarketData
    {
        public DrugType drugType;
        public float currentPrice;
        public float basePrice;
        public float demand; // 0-2 multiplier
        public float supply; // 0-2 multiplier
        public float volatility; // How much price can change
        public float lastUpdate;
        
        public void UpdatePrice()
        {
            // Simple supply/demand pricing model
            float demandEffect = demand - 1f; // -1 to +1
            float supplyEffect = (2f - supply) - 1f; // -1 to +1 (inverted)
            
            float priceChange = (demandEffect + supplyEffect) * volatility * Random.Range(0.5f, 1.5f);
            currentPrice = Mathf.Max(basePrice * 0.1f, currentPrice + priceChange);
            
            lastUpdate = Time.time;
        }
        
        public float GetPriceMultiplier()
        {
            return currentPrice / basePrice;
        }
    }
    
    /// <summary>
    /// Customer data for bulk sales
    /// </summary>
    [System.Serializable]
    public class BulkCustomer
    {
        public string name;
        public CustomerType type;
        public DrugType preferredDrug;
        public int minQuantity;
        public int maxQuantity;
        public float priceMultiplier; // How much they pay compared to street price
        public float riskLevel; // Chance of being a setup
        public float loyaltyLevel; // Affects repeat business
        public System.DateTime lastVisit;
        public int totalPurchases;
        
        public bool IsAvailable()
        {
            var daysSinceLastVisit = (System.DateTime.Now - lastVisit).Days;
            var baseChance = 0.1f; // 10% base chance per day
            var loyaltyBonus = loyaltyLevel * 0.05f; // Up to 5% bonus
            
            return Random.value < (baseChance + loyaltyBonus) * daysSinceLastVisit;
        }
        
        public int GetDesiredQuantity()
        {
            return Random.Range(minQuantity, maxQuantity + 1);
        }
    }
    
    public enum CustomerType
    {
        BikerGang,
        BusinessPeople,
        College,
        Tourists,
        LocalDealers,
        NightClubOwners,
        Musicians,
        Athletes
    }
    
    /// <summary>
    /// Economic events that affect the market
    /// </summary>
    [System.Serializable]
    public class EconomicEvent
    {
        public string name;
        public string description;
        public EventType type;
        public float duration; // In game days
        public float startTime;
        public Dictionary<DrugType, float> priceEffects;
        public Dictionary<DrugType, float> demandEffects;
        public bool isActive;
        
        public void Activate()
        {
            isActive = true;
            startTime = Time.time;
            Debug.Log($"Economic event activated: {name}");
        }
        
        public void Deactivate()
        {
            isActive = false;
            Debug.Log($"Economic event ended: {name}");
        }
        
        public bool ShouldEnd()
        {
            return isActive && (Time.time - startTime) >= (duration * 86400f); // Convert days to seconds
        }
    }
    
    public enum EventType
    {
        Festival,
        PoliceRaid,
        DrugBust,
        EconomicCrisis,
        Holiday,
        WeatherEvent,
        PoliticalEvent
    }
}
