Payload for "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.UnityAdditionalFile.txt"

C:\Users\<USER>\Desktop\GAMEE    

Payload for "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp"

-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.ref.dll"
-define:UNITY_2022_3_45
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-analyzer:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/Scripts/Cartel/CartelData.cs"
"Assets/Scripts/Cartel/CartelManager.cs"
"Assets/Scripts/Core/GameManager.cs"
"Assets/Scripts/Core/SimpleGameManager.cs"
"Assets/Scripts/Drugs/DrugData.cs"
"Assets/Scripts/Drugs/DrugManager.cs"
"Assets/Scripts/Economy/EconomyData.cs"
"Assets/Scripts/Economy/MoneyManager.cs"
"Assets/Scripts/Environment/BuildingInteraction.cs"
"Assets/Scripts/Environment/CityGenerator.cs"
"Assets/Scripts/Features/BulkSalesManager.cs"
"Assets/Scripts/Features/FishingManager.cs"
"Assets/Scripts/Player/CameraController.cs"
"Assets/Scripts/Player/PlayerController.cs"
"Assets/Scripts/Player/SimplePlayerController.cs"
"Assets/Scripts/Police/PoliceData.cs"
"Assets/Scripts/Police/PoliceManager.cs"
"Assets/Scripts/Property/PropertyData.cs"
"Assets/Scripts/Property/PropertyManager.cs"
"Assets/Scripts/UI/SimpleUIManager.cs"
"Assets/Scripts/UI/UIManager.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.UnityAdditionalFile.txt"    

Payload for "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp2"

    

Payload for "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm.rsp"

Library\Bee\artifacts\mvdfrm\UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.dll_12D88E704AB8989C.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.SceneViewModule.dll_D3F734F5833FEADB.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.UIElementsModule.dll_067413C2B719E945.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AccessibilityModule.dll_648FDA6173531F0D.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AndroidJNIModule.dll_84BCFC960039581B.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ARModule.dll_CD1FE17E093B02A6.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.AudioModule.dll_27F5F86C90D0FEA5.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ClothModule.dll_8EDE55676AEDDD89.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.CoreModule.dll_FCC7985BFE103BD4.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.CrashReportingModule.dll_28C9303179F47BEC.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.DirectorModule.dll_EF8575F8F180DEF1.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.dll_457CD548A92C2581.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.GIModule.dll_ABE9658847CC8BF7.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.IMGUIModule.dll_605597D357029286.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.LocalizationModule.dll_1988DFD28A3A999E.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.PhysicsModule.dll_864B31AF48902016.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.PropertiesModule.dll_DC4585EF3965AD48.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.StreamingModule.dll_1861BB66B801A9B1.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.SubsystemsModule.dll_02E41FA2B631C699.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TerrainModule.dll_A091AD1E1E4E6904.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityCurlModule.dll_96E96F47D88DE460.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm
Library\Bee\artifacts\mvdfrm\UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm    

