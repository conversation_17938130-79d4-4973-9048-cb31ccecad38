using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Simplified Game Manager to get the project running without errors
    /// </summary>
    public class SimpleGameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public float gameSpeed = 1.0f;
        public bool isPaused = false;
        
        // Singleton pattern
        public static SimpleGameManager Instance { get; private set; }
        
        // Game state
        public string currentState = "Playing";
        
        // Simple money tracking
        public float cleanMoney = 1000f;
        public float dirtyMoney = 0f;
        public float policeHeat = 0f;
        public float corruption = 0f;
        
        // Events
        public System.Action<string> OnGameStateChanged;
        public System.Action<float> OnGameSpeedChanged;
        public System.Action<float, float> OnMoneyChanged;
        public System.Action<float> OnHeatChanged;
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetGameState("Playing");
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
            OnHeatChanged?.Invoke(policeHeat);
        }
        
        private void Update()
        {
            HandleInput();
            UpdateSystems();
        }
        
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.Space))
            {
                TogglePause();
            }
            
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                ToggleMenu();
            }
            
            // Speed controls
            if (Input.GetKeyDown(KeyCode.Alpha1)) SetGameSpeed(0.5f);
            if (Input.GetKeyDown(KeyCode.Alpha2)) SetGameSpeed(1.0f);
            if (Input.GetKeyDown(KeyCode.Alpha3)) SetGameSpeed(2.0f);
            if (Input.GetKeyDown(KeyCode.Alpha4)) SetGameSpeed(3.0f);
            if (Input.GetKeyDown(KeyCode.Alpha5)) SetGameSpeed(5.0f);
        }
        
        private void UpdateSystems()
        {
            // Simple passive income
            if (currentState == "Playing")
            {
                cleanMoney += 10f * Time.deltaTime; // $10 per second

                // Slowly reduce heat
                if (policeHeat > 0)
                {
                    policeHeat = Mathf.Max(0, policeHeat - 5f * Time.deltaTime);
                    OnHeatChanged?.Invoke(policeHeat);
                }

                OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
            }
        }
        
        private void InitializeGame()
        {
            Debug.Log("Street Empire - Simple Game Manager Initialized");
            Time.timeScale = gameSpeed;
        }
        
        public void SetGameState(string newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                OnGameStateChanged?.Invoke(currentState);

                switch (currentState)
                {
                    case "Playing":
                        Time.timeScale = gameSpeed;
                        break;
                    case "Paused":
                        Time.timeScale = 0f;
                        break;
                    case "Menu":
                        Time.timeScale = 0f;
                        break;
                }
            }
        }
        
        public void TogglePause()
        {
            if (currentState == "Playing")
            {
                SetGameState("Paused");
                Debug.Log("Game Paused");
            }
            else if (currentState == "Paused")
            {
                SetGameState("Playing");
                Debug.Log("Game Resumed");
            }
        }
        
        public void ToggleMenu()
        {
            if (currentState == "Menu")
            {
                SetGameState("Playing");
            }
            else
            {
                SetGameState("Menu");
            }
        }
        
        public void SetGameSpeed(float speed)
        {
            gameSpeed = Mathf.Clamp(speed, 0.1f, 5.0f);
            if (currentState == "Playing")
            {
                Time.timeScale = gameSpeed;
            }
            OnGameSpeedChanged?.Invoke(gameSpeed);
            Debug.Log($"Game Speed: {gameSpeed}x");
        }
        
        // Simple drug dealing simulation
        public void SellDrugs(float amount)
        {
            dirtyMoney += amount;
            policeHeat += amount * 0.1f; // Increase heat
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
            OnHeatChanged?.Invoke(policeHeat);
            Debug.Log($"Sold drugs for ${amount:F0}. Heat increased to {policeHeat:F1}");
        }
        
        public void LaunderMoney(float amount)
        {
            if (dirtyMoney >= amount)
            {
                dirtyMoney -= amount;
                cleanMoney += amount * 0.85f; // 15% fee
                OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
                Debug.Log($"Laundered ${amount:F0} (Fee: 15%)");
            }
        }
        
        public void BribePolice(float amount)
        {
            if (cleanMoney >= amount)
            {
                cleanMoney -= amount;
                policeHeat = Mathf.Max(0, policeHeat - amount * 0.5f);
                corruption += amount * 0.1f;
                OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
                OnHeatChanged?.Invoke(policeHeat);
                Debug.Log($"Bribed police for ${amount:F0}. Heat reduced to {policeHeat:F1}");
            }
        }
        
        public void SaveGame()
        {
            Debug.Log("Game Saved (Simple Version)");
        }
        
        public void LoadGame()
        {
            Debug.Log("Game Loaded (Simple Version)");
        }
        
        public void QuitGame()
        {
            Debug.Log("Quitting Game");
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
    

}
