using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Handles player interaction with buildings in the city
    /// </summary>
    public class BuildingInteraction : MonoBehaviour
    {
        [Header("Building Info")]
        public BlockType blockType;
        public Vector3 buildingPosition;
        public bool isInteractable = true;
        
        [Header("Interaction Settings")]
        public float interactionRange = 5f;
        public KeyCode interactionKey = KeyCode.E;
        
        [Header("Visual Feedback")]
        public GameObject highlightEffect;
        public Color highlightColor = Color.yellow;
        
        private bool playerInRange = false;
        private Renderer buildingRenderer;
        private Color originalColor;
        
        private void Start()
        {
            buildingRenderer = GetComponent<Renderer>();
            if (buildingRenderer != null)
            {
                originalColor = buildingRenderer.material.color;
            }
        }
        
        private void Update()
        {
            CheckPlayerProximity();
            HandleInteraction();
        }
        
        private void CheckPlayerProximity()
        {
            // Find player (assuming player has "Player" tag)
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player == null) return;
            
            float distance = Vector3.Distance(transform.position, player.transform.position);
            bool wasInRange = playerInRange;
            playerInRange = distance <= interactionRange;
            
            // Visual feedback when player enters/exits range
            if (playerInRange != wasInRange)
            {
                UpdateVisualFeedback();
            }
        }
        
        private void UpdateVisualFeedback()
        {
            if (buildingRenderer == null) return;
            
            if (playerInRange && isInteractable)
            {
                buildingRenderer.material.color = highlightColor;
                if (highlightEffect != null)
                {
                    highlightEffect.SetActive(true);
                }
            }
            else
            {
                buildingRenderer.material.color = originalColor;
                if (highlightEffect != null)
                {
                    highlightEffect.SetActive(false);
                }
            }
        }
        
        private void HandleInteraction()
        {
            if (playerInRange && isInteractable && Input.GetKeyDown(interactionKey))
            {
                InteractWithBuilding();
            }
        }
        
        private void InteractWithBuilding()
        {
            switch (blockType)
            {
                case BlockType.Residential:
                    HandleResidentialInteraction();
                    break;
                case BlockType.Commercial:
                    HandleCommercialInteraction();
                    break;
                case BlockType.Industrial:
                    HandleIndustrialInteraction();
                    break;
                case BlockType.Special:
                    HandleSpecialInteraction();
                    break;
            }
        }
        
        private void HandleResidentialInteraction()
        {
            // Residential buildings can be potential safe houses or customer locations
            Debug.Log("Interacting with residential building");
            
            // Check if this could be a property for sale
            if (Random.value < 0.3f) // 30% chance
            {
                ShowPropertyPurchaseOption();
            }
            else
            {
                ShowCustomerInteraction();
            }
        }
        
        private void HandleCommercialInteraction()
        {
            // Commercial buildings can be businesses for money laundering
            Debug.Log("Interacting with commercial building");
            
            if (Random.value < 0.4f) // 40% chance
            {
                ShowBusinessPurchaseOption();
            }
            else
            {
                ShowSupplierInteraction();
            }
        }
        
        private void HandleIndustrialInteraction()
        {
            // Industrial buildings can be labs or warehouses
            Debug.Log("Interacting with industrial building");
            
            if (Random.value < 0.5f) // 50% chance
            {
                ShowLabPurchaseOption();
            }
            else
            {
                ShowWarehouseInteraction();
            }
        }
        
        private void HandleSpecialInteraction()
        {
            // Special buildings have unique interactions
            Debug.Log("Interacting with special building");
            
            // Could be police station, cartel hideout, etc.
            ShowSpecialBuildingOptions();
        }
        
        private void ShowPropertyPurchaseOption()
        {
            // In a full implementation, this would open a UI panel
            var propertyManager = GameManager.Instance?.propertyManager;
            if (propertyManager != null)
            {
                Debug.Log("Property available for purchase!");
                // propertyManager.ShowPropertyPurchaseUI(this);
            }
        }
        
        private void ShowBusinessPurchaseOption()
        {
            var moneyManager = GameManager.Instance?.moneyManager;
            if (moneyManager != null)
            {
                Debug.Log("Business available for purchase!");
                // Show business purchase UI
            }
        }
        
        private void ShowLabPurchaseOption()
        {
            Debug.Log("Laboratory space available!");
            // Show lab purchase/rental options
        }
        
        private void ShowCustomerInteraction()
        {
            var drugManager = GameManager.Instance?.drugManager;
            if (drugManager != null)
            {
                Debug.Log("Potential customer found!");
                // Show drug selling interface
            }
        }
        
        private void ShowSupplierInteraction()
        {
            Debug.Log("Supplier available!");
            // Show ingredient/equipment purchase options
        }
        
        private void ShowWarehouseInteraction()
        {
            Debug.Log("Warehouse space available!");
            // Show storage options
        }
        
        private void ShowSpecialBuildingOptions()
        {
            Debug.Log("Special building interaction!");
            // Context-specific interactions
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw interaction range in editor
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, interactionRange);
        }
    }
}
