using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Data structures for cartel system
    /// </summary>
    
    [System.Serializable]
    public class Cartel
    {
        public string id;
        public string name;
        public float strength; // Overall power level
        public float aggression; // How likely to attack
        public List<int> territory; // Territory IDs owned
        public float resources; // Money/equipment
        public CartelSpecialization specialization;
        public List<CartelMember> members;
        
        public float GetTotalPower()
        {
            float memberBonus = members.Count * 5f;
            float territoryBonus = territory.Count * 10f;
            return strength + memberBonus + territoryBonus;
        }
        
        public Color GetCartelColor()
        {
            switch (specialization)
            {
                case CartelSpecialization.DrugProduction: return Color.green;
                case CartelSpecialization.Smuggling: return Color.blue;
                case CartelSpecialization.Violence: return Color.red;
                case CartelSpecialization.Corruption: return Color.yellow;
                default: return Color.gray;
            }
        }
    }
    
    [System.Serializable]
    public class PlayerCartel
    {
        public string name;
        public float reputation;
        public List<int> territory;
        public List<CartelMember> members;
        public float resources;
        public Dictionary<string, float> relationships; // Cartel name -> relationship (-1 to 1)
        
        public void AddReputation(float amount)
        {
            reputation += amount;
            reputation = Mathf.Max(0f, reputation);
        }
        
        public float GetTerritoryIncome()
        {
            return territory.Count * 100f; // Base income per territory
        }
    }
    
    [System.Serializable]
    public class CartelMember
    {
        public string name;
        public MemberRole role;
        public float skill; // 0-100
        public float loyalty; // 0-100
        public float salary;
        public bool isActive;
        public System.DateTime hireDate;
        
        public float GetEffectiveness()
        {
            return (skill * loyalty) / 10000f; // 0-1 range
        }
        
        public string GetRoleDescription()
        {
            switch (role)
            {
                case MemberRole.Enforcer: return "Handles violence and intimidation";
                case MemberRole.Chemist: return "Improves drug production quality";
                case MemberRole.Dealer: return "Manages street-level sales";
                case MemberRole.Smuggler: return "Handles transportation and logistics";
                case MemberRole.Accountant: return "Manages money laundering operations";
                case MemberRole.Informant: return "Provides intelligence on police and rivals";
                default: return "General operations";
            }
        }
    }
    
    [System.Serializable]
    public class TerritoryData
    {
        public int id;
        public string name;
        public string owner; // "Player", "Neutral", or cartel name
        public float value; // Income potential
        public float securityLevel; // How well defended
        public Dictionary<DrugType, float> drugDemand; // Local demand for each drug
        public Vector3 location;
        public TerritoryType type;
        
        public float GetDailyIncome()
        {
            return value * (1f + securityLevel * 0.5f);
        }
        
        public Color GetOwnerColor()
        {
            switch (owner)
            {
                case "Player": return Color.green;
                case "Neutral": return Color.gray;
                default: return Color.red; // Enemy cartel
            }
        }
    }
    
    [System.Serializable]
    public class CartelConflict
    {
        public string id;
        public string attackingCartel;
        public string defendingCartel;
        public int territoryId; // -1 for general conflicts
        public float startTime;
        public bool isPlayerInvolved;
        public ConflictType conflictType;
        public ConflictStatus status;
        
        public float GetDuration()
        {
            return Time.time - startTime;
        }
        
        public string GetDescription()
        {
            switch (conflictType)
            {
                case ConflictType.TerritoryAttack:
                    return $"{attackingCartel} is attacking {defendingCartel}'s territory";
                case ConflictType.Ambush:
                    return $"{attackingCartel} has ambushed {defendingCartel}";
                case ConflictType.CartelWar:
                    return $"Full-scale war between {attackingCartel} and {defendingCartel}";
                case ConflictType.Sabotage:
                    return $"{attackingCartel} is sabotaging {defendingCartel}'s operations";
                default:
                    return "Unknown conflict type";
            }
        }
    }
    
    [System.Serializable]
    public class CartelMeeting
    {
        public string cartelName;
        public MeetingType meetingType;
        public Vector3 location;
        public float scheduledTime;
        public float reward;
        public float risk; // Chance of ambush/setup
        public bool isCompleted;
        
        public string GetDescription()
        {
            switch (meetingType)
            {
                case MeetingType.DrugDeal:
                    return $"Large drug transaction with {cartelName}";
                case MeetingType.TerritoryNegotiation:
                    return $"Negotiate territory boundaries with {cartelName}";
                case MeetingType.Alliance:
                    return $"Discuss potential alliance with {cartelName}";
                case MeetingType.Information:
                    return $"Intelligence exchange with {cartelName}";
                case MeetingType.Ambush:
                    return $"Suspicious meeting request from {cartelName}";
                default:
                    return "Unknown meeting type";
            }
        }
        
        public Color GetRiskColor()
        {
            if (risk < 0.3f) return Color.green;
            if (risk < 0.7f) return Color.yellow;
            return Color.red;
        }
    }
    
    public enum CartelSpecialization
    {
        DrugProduction,
        Smuggling,
        Violence,
        Corruption,
        MoneyLaundering
    }
    
    public enum MemberRole
    {
        Enforcer,
        Chemist,
        Dealer,
        Smuggler,
        Accountant,
        Informant,
        Lieutenant
    }
    
    public enum TerritoryType
    {
        Residential,
        Commercial,
        Industrial,
        Entertainment,
        University,
        Port,
        Airport
    }
    
    public enum ConflictType
    {
        TerritoryAttack,
        Ambush,
        CartelWar,
        Sabotage,
        Retaliation
    }
    
    public enum ConflictStatus
    {
        Active,
        PlayerVictory,
        PlayerDefeat,
        Stalemate,
        Resolved
    }
    
    public enum MeetingType
    {
        DrugDeal,
        TerritoryNegotiation,
        Alliance,
        Information,
        Ambush
    }
    
    /// <summary>
    /// Cartel AI behavior patterns
    /// </summary>
    [System.Serializable]
    public class CartelAI
    {
        public string cartelId;
        public AIPersonality personality;
        public float aggressionLevel;
        public float expansionDesire;
        public float riskTolerance;
        public List<string> preferredTargets; // Other cartel IDs
        public List<string> allies;
        public List<string> enemies;
        
        public bool ShouldAttack(Cartel target, float playerThreat)
        {
            float attackChance = aggressionLevel;
            
            // More likely to attack if target is weak
            if (target.strength < 50f) attackChance += 0.2f;
            
            // Less likely if player is strong threat
            if (playerThreat > 80f) attackChance -= 0.3f;
            
            return Random.value < attackChance;
        }
        
        public bool ShouldExpand()
        {
            return Random.value < expansionDesire;
        }
    }
    
    public enum AIPersonality
    {
        Aggressive,
        Defensive,
        Opportunistic,
        Diplomatic,
        Unpredictable
    }
}
