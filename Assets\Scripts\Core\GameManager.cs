using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Main game manager that controls overall game state and coordinates between systems
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public float gameSpeed = 1.0f;
        public bool isPaused = false;
        
        [Header("Systems")]
        public DrugManager drugManager;
        public MoneyManager moneyManager;
        public PoliceManager policeManager;
        public CartelManager cartelManager;
        public PropertyManager propertyManager;
        public UIManager uiManager;
        
        // Singleton pattern
        public static GameManager Instance { get; private set; }
        
        // Game state
        public GameState currentState = GameState.Playing;
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<float> OnGameSpeedChanged;
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetGameState(GameState.Playing);
        }
        
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.Space))
            {
                TogglePause();
            }
            
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                ToggleMenu();
            }
        }
        
        private void InitializeGame()
        {
            // Initialize all game systems
            Debug.Log("Street Empire - Initializing Game Systems...");
            
            // Set up initial game state
            Time.timeScale = gameSpeed;
        }
        
        public void SetGameState(GameState newState)
        {
            if (currentState != newState)
            {
                currentState = newState;
                OnGameStateChanged?.Invoke(currentState);
                
                switch (currentState)
                {
                    case GameState.Playing:
                        Time.timeScale = gameSpeed;
                        break;
                    case GameState.Paused:
                        Time.timeScale = 0f;
                        break;
                    case GameState.Menu:
                        Time.timeScale = 0f;
                        break;
                }
            }
        }
        
        public void TogglePause()
        {
            if (currentState == GameState.Playing)
            {
                SetGameState(GameState.Paused);
            }
            else if (currentState == GameState.Paused)
            {
                SetGameState(GameState.Playing);
            }
        }
        
        public void ToggleMenu()
        {
            if (currentState == GameState.Menu)
            {
                SetGameState(GameState.Playing);
            }
            else
            {
                SetGameState(GameState.Menu);
            }
        }
        
        public void SetGameSpeed(float speed)
        {
            gameSpeed = Mathf.Clamp(speed, 0.1f, 5.0f);
            if (currentState == GameState.Playing)
            {
                Time.timeScale = gameSpeed;
            }
            OnGameSpeedChanged?.Invoke(gameSpeed);
        }
        
        public void SaveGame()
        {
            // Implement save functionality
            Debug.Log("Game Saved");
        }
        
        public void LoadGame()
        {
            // Implement load functionality
            Debug.Log("Game Loaded");
        }
        
        public void QuitGame()
        {
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
    
    public enum GameState
    {
        Playing,
        Paused,
        Menu,
        Loading
    }
}
