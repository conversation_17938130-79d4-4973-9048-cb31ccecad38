using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages all UI elements and user interface interactions
    /// </summary>
    public class UIManager : MonoBehaviour
    {
        [Header("Main UI Panels")]
        public GameObject mainMenuPanel;
        public GameObject gameplayPanel;
        public GameObject pausePanel;
        public GameObject settingsPanel;
        
        [Header("HUD Elements")]
        public Text cleanMoneyText;
        public Text dirtyMoneyText;
        public Slider heatSlider;
        public Slider corruptionSlider;
        public Text gameSpeedText;
        
        [Header("Drug Production UI")]
        public GameObject drugProductionPanel;
        public Transform productionQueueParent;
        public GameObject productionItemPrefab;
        
        [Header("Property UI")]
        public GameObject propertyPanel;
        public Transform propertyListParent;
        public GameObject propertyItemPrefab;
        
        [Header("Cartel UI")]
        public GameObject cartelPanel;
        public Transform cartelListParent;
        public GameObject cartelItemPrefab;
        public GameObject territoryMapPanel;
        
        [Header("Notifications")]
        public GameObject notificationPanel;
        public Transform notificationParent;
        public GameObject notificationPrefab;
        
        [Header("Police UI")]
        public GameObject policePanel;
        public Transform investigationListParent;
        public GameObject investigationItemPrefab;
        public GameObject raidWarningPanel;
        
        // UI State
        private Dictionary<string, GameObject> activePanels = new Dictionary<string, GameObject>();
        private Queue<Notification> notificationQueue = new Queue<Notification>();
        
        private void Start()
        {
            InitializeUI();
            SubscribeToEvents();
        }
        
        private void Update()
        {
            UpdateHUD();
            ProcessNotificationQueue();
        }
        
        private void InitializeUI()
        {
            // Initialize panel dictionary
            activePanels["main"] = mainMenuPanel;
            activePanels["gameplay"] = gameplayPanel;
            activePanels["pause"] = pausePanel;
            activePanels["settings"] = settingsPanel;
            activePanels["drugs"] = drugProductionPanel;
            activePanels["property"] = propertyPanel;
            activePanels["cartel"] = cartelPanel;
            activePanels["police"] = policePanel;
            
            // Start with gameplay panel active
            ShowPanel("gameplay");
        }
        
        private void SubscribeToEvents()
        {
            var gameManager = GameManager.Instance;
            
            // Money events
            if (gameManager.moneyManager != null)
            {
                gameManager.moneyManager.OnMoneyChanged += UpdateMoneyDisplay;
                gameManager.moneyManager.OnTransactionMade += ShowTransactionNotification;
            }
            
            // Police events
            if (gameManager.policeManager != null)
            {
                gameManager.policeManager.OnHeatChanged += UpdateHeatDisplay;
                gameManager.policeManager.OnCorruptionChanged += UpdateCorruptionDisplay;
                gameManager.policeManager.OnRaidStarted += ShowRaidWarning;
                gameManager.policeManager.OnInvestigationStarted += ShowInvestigationNotification;
            }
            
            // Drug events
            if (gameManager.drugManager != null)
            {
                gameManager.drugManager.OnProductionStarted += ShowProductionNotification;
                gameManager.drugManager.OnProductionCompleted += ShowProductionCompleteNotification;
            }
            
            // Cartel events
            if (gameManager.cartelManager != null)
            {
                gameManager.cartelManager.OnConflictStarted += ShowCartelConflictNotification;
                gameManager.cartelManager.OnTerritoryChanged += ShowTerritoryChangeNotification;
            }
            
            // Game state events
            gameManager.OnGameSpeedChanged += UpdateGameSpeedDisplay;
        }
        
        private void UpdateHUD()
        {
            // Update game speed display
            if (gameSpeedText != null)
            {
                gameSpeedText.text = $"Speed: {GameManager.Instance.gameSpeed:F1}x";
            }
        }
        
        private void UpdateMoneyDisplay(float cleanMoney, float dirtyMoney)
        {
            if (cleanMoneyText != null)
            {
                cleanMoneyText.text = $"Clean: ${cleanMoney:F0}";
                cleanMoneyText.color = Color.green;
            }
            
            if (dirtyMoneyText != null)
            {
                dirtyMoneyText.text = $"Dirty: ${dirtyMoney:F0}";
                dirtyMoneyText.color = Color.red;
            }
        }
        
        private void UpdateHeatDisplay(float heat)
        {
            if (heatSlider != null)
            {
                heatSlider.value = heat / 100f; // Assuming max heat is 100
                
                // Change color based on heat level
                var fillImage = heatSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (heat < 30f) fillImage.color = Color.green;
                    else if (heat < 70f) fillImage.color = Color.yellow;
                    else fillImage.color = Color.red;
                }
            }
        }
        
        private void UpdateCorruptionDisplay(float corruption)
        {
            if (corruptionSlider != null)
            {
                corruptionSlider.value = corruption / 100f; // Assuming max corruption is 100
                
                var fillImage = corruptionSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    fillImage.color = Color.yellow;
                }
            }
        }
        
        private void UpdateGameSpeedDisplay(float speed)
        {
            if (gameSpeedText != null)
            {
                gameSpeedText.text = $"Speed: {speed:F1}x";
            }
        }
        
        public void ShowPanel(string panelName)
        {
            // Hide all panels
            foreach (var panel in activePanels.Values)
            {
                if (panel != null) panel.SetActive(false);
            }
            
            // Show requested panel
            if (activePanels.ContainsKey(panelName) && activePanels[panelName] != null)
            {
                activePanels[panelName].SetActive(true);
            }
        }
        
        public void TogglePanel(string panelName)
        {
            if (activePanels.ContainsKey(panelName) && activePanels[panelName] != null)
            {
                bool isActive = activePanels[panelName].activeSelf;
                activePanels[panelName].SetActive(!isActive);
            }
        }
        
        private void ShowTransactionNotification(Transaction transaction)
        {
            string message = $"{transaction.type}: ${transaction.amount:F0} ({transaction.description})";
            Color color = transaction.type == TransactionType.Income ? Color.green : Color.red;
            
            QueueNotification(message, color, 3f);
        }
        
        private void ShowProductionNotification(ProductionOrder order)
        {
            string message = $"Started producing {order.quantity} {order.drugType}";
            QueueNotification(message, Color.blue, 2f);
        }
        
        private void ShowProductionCompleteNotification(ProductionOrder order)
        {
            string message = $"Completed: {order.quantity} {order.drugType}";
            QueueNotification(message, Color.green, 3f);
        }
        
        private void ShowRaidWarning(Raid raid)
        {
            if (raidWarningPanel != null)
            {
                raidWarningPanel.SetActive(true);
                
                // Auto-hide after warning time
                Invoke(nameof(HideRaidWarning), raid.warningTime);
            }
            
            string message = $"RAID WARNING: {raid.GetDescription()}";
            QueueNotification(message, Color.red, 5f);
        }
        
        private void HideRaidWarning()
        {
            if (raidWarningPanel != null)
            {
                raidWarningPanel.SetActive(false);
            }
        }
        
        private void ShowInvestigationNotification(Investigation investigation)
        {
            string message = $"Investigation started: {investigation.type}";
            QueueNotification(message, Color.orange, 4f);
        }
        
        private void ShowCartelConflictNotification(CartelConflict conflict)
        {
            string message = conflict.GetDescription();
            QueueNotification(message, Color.red, 4f);
        }
        
        private void ShowTerritoryChangeNotification(int territoryId, string newOwner)
        {
            string message = $"Territory {territoryId} now controlled by {newOwner}";
            Color color = newOwner == "Player" ? Color.green : Color.red;
            QueueNotification(message, color, 3f);
        }
        
        private void QueueNotification(string message, Color color, float duration)
        {
            var notification = new Notification
            {
                message = message,
                color = color,
                duration = duration,
                timestamp = Time.time
            };
            
            notificationQueue.Enqueue(notification);
        }
        
        private void ProcessNotificationQueue()
        {
            if (notificationQueue.Count > 0 && notificationParent != null && notificationPrefab != null)
            {
                var notification = notificationQueue.Dequeue();
                ShowNotification(notification);
            }
        }
        
        private void ShowNotification(Notification notification)
        {
            var notificationObj = Instantiate(notificationPrefab, notificationParent);
            var textComponent = notificationObj.GetComponentInChildren<Text>();

            if (textComponent != null)
            {
                textComponent.text = notification.message;
                textComponent.color = notification.color;
            }

            // Auto-destroy after duration
            Destroy(notificationObj, notification.duration);
        }
        
        // UI Button Handlers
        public void OnPauseButtonClicked()
        {
            GameManager.Instance.TogglePause();
        }
        
        public void OnSpeedButtonClicked(float speed)
        {
            GameManager.Instance.SetGameSpeed(speed);
        }
        
        public void OnSaveButtonClicked()
        {
            GameManager.Instance.SaveGame();
        }
        
        public void OnLoadButtonClicked()
        {
            GameManager.Instance.LoadGame();
        }
        
        public void OnQuitButtonClicked()
        {
            GameManager.Instance.QuitGame();
        }
        
        public void OnDrugPanelButtonClicked()
        {
            TogglePanel("drugs");
        }
        
        public void OnPropertyPanelButtonClicked()
        {
            TogglePanel("property");
        }
        
        public void OnCartelPanelButtonClicked()
        {
            TogglePanel("cartel");
        }
        
        public void OnPolicePanelButtonClicked()
        {
            TogglePanel("police");
        }
    }
    
    [System.Serializable]
    public class Notification
    {
        public string message;
        public Color color;
        public float duration;
        public float timestamp;
    }
}
