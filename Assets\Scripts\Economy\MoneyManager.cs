using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages all money-related operations including dirty money, laundering, and legitimate businesses
    /// </summary>
    public class MoneyManager : MonoBehaviour
    {
        [Header("Starting Money")]
        public float startingCleanMoney = 1000f;
        public float startingDirtyMoney = 0f;
        
        [Header("Laundering Settings")]
        public float launderingFee = 0.15f; // 15% fee for laundering
        public float maxLaunderingPerDay = 10000f;
        
        // Money tracking
        private float cleanMoney;
        private float dirtyMoney;
        private float totalLaunderedToday;
        private float lastLaunderingReset;
        
        // Transaction history
        private List<Transaction> transactionHistory = new List<Transaction>();
        
        // Legitimate businesses for laundering
        private List<Business> ownedBusinesses = new List<Business>();
        
        // Events
        public System.Action<float, float> OnMoneyChanged;
        public System.Action<Transaction> OnTransactionMade;
        public System.Action<Business> OnBusinessPurchased;
        
        private void Start()
        {
            cleanMoney = startingCleanMoney;
            dirtyMoney = startingDirtyMoney;
            lastLaunderingReset = Time.time;
            
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
        }
        
        private void Update()
        {
            // Reset daily laundering limit
            if (Time.time - lastLaunderingReset >= 86400f) // 24 hours in seconds
            {
                totalLaunderedToday = 0f;
                lastLaunderingReset = Time.time;
            }
            
            // Generate passive income from businesses
            UpdateBusinessIncome();
        }
        
        public bool SpendCleanMoney(float amount, string description = "Purchase")
        {
            if (cleanMoney >= amount)
            {
                cleanMoney -= amount;
                RecordTransaction(TransactionType.Expense, amount, description, true);
                OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
                return true;
            }
            return false;
        }
        
        public bool SpendDirtyMoney(float amount, string description = "Illegal Purchase")
        {
            if (dirtyMoney >= amount)
            {
                dirtyMoney -= amount;
                RecordTransaction(TransactionType.Expense, amount, description, false);
                OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
                return true;
            }
            return false;
        }
        
        public void AddCleanMoney(float amount, string description = "Income")
        {
            cleanMoney += amount;
            RecordTransaction(TransactionType.Income, amount, description, true);
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
        }
        
        public void AddDirtyMoney(float amount, string description = "Drug Sale")
        {
            dirtyMoney += amount;
            RecordTransaction(TransactionType.Income, amount, description, false);
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
        }
        
        public bool LaunderMoney(float amount)
        {
            if (dirtyMoney < amount)
            {
                Debug.Log("Insufficient dirty money to launder");
                return false;
            }
            
            if (totalLaunderedToday + amount > maxLaunderingPerDay)
            {
                Debug.Log("Daily laundering limit exceeded");
                return false;
            }
            
            if (ownedBusinesses.Count == 0)
            {
                Debug.Log("No businesses available for laundering");
                return false;
            }
            
            // Calculate laundering capacity based on businesses
            float totalCapacity = GetTotalLaunderingCapacity();
            if (amount > totalCapacity)
            {
                Debug.Log("Amount exceeds business laundering capacity");
                return false;
            }
            
            // Process laundering
            float fee = amount * launderingFee;
            float cleanAmount = amount - fee;
            
            dirtyMoney -= amount;
            cleanMoney += cleanAmount;
            totalLaunderedToday += amount;
            
            RecordTransaction(TransactionType.Laundering, amount, $"Laundered ${amount:F2} (Fee: ${fee:F2})", false);
            OnMoneyChanged?.Invoke(cleanMoney, dirtyMoney);
            
            Debug.Log($"Laundered ${amount:F2}, received ${cleanAmount:F2} clean money");
            return true;
        }
        
        public bool PurchaseBusiness(BusinessType businessType)
        {
            var businessData = GetBusinessData(businessType);
            if (businessData == null) return false;
            
            if (!SpendCleanMoney(businessData.purchasePrice, $"Purchased {businessData.name}"))
            {
                return false;
            }
            
            var business = new Business
            {
                type = businessType,
                name = businessData.name,
                purchasePrice = businessData.purchasePrice,
                dailyIncome = businessData.dailyIncome,
                launderingCapacity = businessData.launderingCapacity,
                purchaseDate = System.DateTime.Now,
                lastIncomeGenerated = Time.time
            };
            
            ownedBusinesses.Add(business);
            OnBusinessPurchased?.Invoke(business);
            
            Debug.Log($"Purchased business: {business.name}");
            return true;
        }
        
        private void UpdateBusinessIncome()
        {
            foreach (var business in ownedBusinesses)
            {
                // Generate income every hour (3600 seconds)
                if (Time.time - business.lastIncomeGenerated >= 3600f)
                {
                    float hourlyIncome = business.dailyIncome / 24f;
                    AddCleanMoney(hourlyIncome, $"Income from {business.name}");
                    business.lastIncomeGenerated = Time.time;
                }
            }
        }
        
        private float GetTotalLaunderingCapacity()
        {
            float total = 0f;
            foreach (var business in ownedBusinesses)
            {
                total += business.launderingCapacity;
            }
            return total;
        }
        
        private void RecordTransaction(TransactionType type, float amount, string description, bool isClean)
        {
            var transaction = new Transaction
            {
                type = type,
                amount = amount,
                description = description,
                isCleanMoney = isClean,
                timestamp = System.DateTime.Now
            };
            
            transactionHistory.Add(transaction);
            OnTransactionMade?.Invoke(transaction);
            
            // Keep only last 100 transactions
            if (transactionHistory.Count > 100)
            {
                transactionHistory.RemoveAt(0);
            }
        }
        
        private BusinessData GetBusinessData(BusinessType type)
        {
            // In a full implementation, this would come from a ScriptableObject database
            switch (type)
            {
                case BusinessType.CarWash:
                    return new BusinessData("Car Wash", 50000f, 500f, 5000f);
                case BusinessType.Restaurant:
                    return new BusinessData("Restaurant", 150000f, 1200f, 15000f);
                case BusinessType.LaundryMat:
                    return new BusinessData("Laundromat", 80000f, 600f, 8000f);
                case BusinessType.NightClub:
                    return new BusinessData("Night Club", 300000f, 2000f, 25000f);
                default:
                    return null;
            }
        }
        
        // Getters
        public float GetCleanMoney() => cleanMoney;
        public float GetDirtyMoney() => dirtyMoney;
        public float GetTotalMoney() => cleanMoney + dirtyMoney;
        public List<Transaction> GetTransactionHistory() => new List<Transaction>(transactionHistory);
        public List<Business> GetOwnedBusinesses() => new List<Business>(ownedBusinesses);
        public float GetDailyLaunderingRemaining() => maxLaunderingPerDay - totalLaunderedToday;
    }
}
