using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Data structures for property system
    /// </summary>
    
    [System.Serializable]
    public class Property
    {
        public string id;
        public string name;
        public PropertyType type;
        public Vector3 location;
        public float purchasePrice;
        public float currentValue;
        public float securityLevel; // 0-1
        public float capacity; // Storage/production capacity
        public bool isOwned;
        public System.DateTime purchaseDate;
        public float lastMaintenance;
        public float lastIncomeGenerated;
        public List<PropertyUpgrade> upgrades;
        
        public float GetTotalSecurityLevel()
        {
            float upgradeBonus = 0f;
            foreach (var upgrade in upgrades)
            {
                upgradeBonus += upgrade.securityBonus;
            }
            return Mathf.Min(1f, securityLevel + upgradeBonus);
        }
        
        public float GetTotalCapacity()
        {
            float upgradeBonus = 0f;
            foreach (var upgrade in upgrades)
            {
                upgradeBonus += upgrade.capacityBonus;
            }
            return capacity + upgradeBonus;
        }
        
        public bool NeedsMaintenance()
        {
            return Time.time - lastMaintenance >= 604800f; // 7 days
        }
        
        public float GetROI()
        {
            if (purchasePrice <= 0f) return 0f;
            
            var daysSincePurchase = (System.DateTime.Now - purchaseDate).Days;
            if (daysSincePurchase <= 0) return 0f;
            
            float totalIncome = 0f;
            if (type == PropertyType.Business)
            {
                totalIncome = daysSincePurchase * 100f; // Simplified calculation
            }
            
            return (totalIncome / purchasePrice) * 100f;
        }
    }
    
    [System.Serializable]
    public class PropertyListing
    {
        public string id;
        public string name;
        public PropertyType type;
        public Vector3 location;
        public float price;
        public float securityLevel;
        public float capacity;
        public string description;
        public bool isAvailable;
        public System.DateTime listingDate;
        
        public bool IsExpired()
        {
            return (System.DateTime.Now - listingDate).Days > 30; // Listings expire after 30 days
        }
        
        public float GetPricePerCapacity()
        {
            return capacity > 0 ? price / capacity : float.MaxValue;
        }
    }
    
    [System.Serializable]
    public class PropertyUpgrade
    {
        public PropertyUpgradeType type;
        public string name;
        public float cost;
        public float securityBonus;
        public float capacityBonus;
        public System.DateTime installDate;
        
        public string GetDescription()
        {
            switch (type)
            {
                case PropertyUpgradeType.SecuritySystem:
                    return "Advanced security cameras and alarms to detect intruders";
                case PropertyUpgradeType.HiddenCompartments:
                    return "Secret storage areas to hide contraband from searches";
                case PropertyUpgradeType.AdvancedEquipment:
                    return "High-tech equipment to increase production capacity";
                case PropertyUpgradeType.EscapeRoute:
                    return "Hidden exits and tunnels for emergency evacuation";
                case PropertyUpgradeType.Soundproofing:
                    return "Noise reduction to avoid attracting attention";
                case PropertyUpgradeType.CleaningStation:
                    return "Chemical cleaning equipment to remove evidence";
                default:
                    return "Property upgrade";
            }
        }
    }
    
    [System.Serializable]
    public class PropertyUpgradeData
    {
        public string name;
        public float cost;
        public float securityBonus;
        public float capacityBonus;
        
        public PropertyUpgradeData(string name, float cost, float security, float capacity)
        {
            this.name = name;
            this.cost = cost;
            this.securityBonus = security;
            this.capacityBonus = capacity;
        }
    }
    
    public enum PropertyType
    {
        SafeHouse,
        Laboratory,
        Warehouse,
        Business,
        Apartment,
        Office,
        Factory
    }
    
    public enum PropertyUpgradeType
    {
        SecuritySystem,
        HiddenCompartments,
        AdvancedEquipment,
        EscapeRoute,
        Soundproofing,
        CleaningStation,
        BackupPower,
        Communications
    }
    
    /// <summary>
    /// Property customization options
    /// </summary>
    [System.Serializable]
    public class PropertyCustomization
    {
        public string propertyId;
        public Dictionary<string, string> decorations; // Room -> decoration type
        public Dictionary<string, Color> colors; // Room -> color scheme
        public List<string> furniture; // Furniture items
        public float totalCost;
        
        public void AddDecoration(string room, string decoration, float cost)
        {
            if (decorations == null) decorations = new Dictionary<string, string>();
            decorations[room] = decoration;
            totalCost += cost;
        }
        
        public void SetRoomColor(string room, Color color, float cost)
        {
            if (colors == null) colors = new Dictionary<string, Color>();
            colors[room] = color;
            totalCost += cost;
        }
    }
    
    /// <summary>
    /// Property events and incidents
    /// </summary>
    [System.Serializable]
    public class PropertyEvent
    {
        public string propertyId;
        public PropertyEventType eventType;
        public float severity; // 0-1
        public string description;
        public System.DateTime occurredAt;
        public bool isResolved;
        public float resolutionCost;
        
        public Color GetSeverityColor()
        {
            if (severity < 0.3f) return Color.green;
            if (severity < 0.7f) return Color.yellow;
            return Color.red;
        }
        
        public string GetEventDescription()
        {
            switch (eventType)
            {
                case PropertyEventType.Break_In:
                    return "Unknown individuals attempted to break into the property";
                case PropertyEventType.Police_Surveillance:
                    return "Police have been spotted surveilling the property";
                case PropertyEventType.Neighbor_Complaint:
                    return "Neighbors have complained about suspicious activity";
                case PropertyEventType.Equipment_Failure:
                    return "Critical equipment has malfunctioned";
                case PropertyEventType.Fire:
                    return "A fire has broken out on the property";
                case PropertyEventType.Flood:
                    return "Water damage has occurred";
                default:
                    return "Unknown incident occurred";
            }
        }
    }
    
    public enum PropertyEventType
    {
        Break_In,
        Police_Surveillance,
        Neighbor_Complaint,
        Equipment_Failure,
        Fire,
        Flood,
        Power_Outage,
        Pest_Infestation
    }
    
    /// <summary>
    /// Property market data for dynamic pricing
    /// </summary>
    [System.Serializable]
    public class PropertyMarket
    {
        public PropertyType propertyType;
        public float averagePrice;
        public float priceGrowth; // Annual percentage
        public float demand; // 0-2 multiplier
        public float supply; // 0-2 multiplier
        public List<PropertyListing> recentSales;
        
        public float GetCurrentMarketMultiplier()
        {
            float demandEffect = demand - 1f; // -1 to +1
            float supplyEffect = (2f - supply) - 1f; // -1 to +1 (inverted)
            
            return 1f + ((demandEffect + supplyEffect) * 0.2f); // ±40% max change
        }
        
        public void UpdateMarket()
        {
            // Simple market simulation
            demand += Random.Range(-0.1f, 0.1f);
            supply += Random.Range(-0.1f, 0.1f);
            
            demand = Mathf.Clamp(demand, 0.1f, 2.0f);
            supply = Mathf.Clamp(supply, 0.1f, 2.0f);
            
            averagePrice *= GetCurrentMarketMultiplier();
        }
    }
}
