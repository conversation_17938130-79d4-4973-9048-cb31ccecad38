using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Player movement and interaction controller
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    public class PlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        public float walkSpeed = 5f;
        public float runSpeed = 10f;
        public float jumpHeight = 2f;
        public float gravity = -9.81f;
        
        [Header("Parkour Settings")]
        public float vaultHeight = 1.5f;
        public float climbHeight = 3f;
        public float parkourRange = 2f;
        public LayerMask parkourLayers = -1;
        
        [Header("Input Settings")]
        public KeyCode runKey = KeyCode.LeftShift;
        public KeyCode jumpKey = KeyCode.Space;
        public KeyCode vaultKey = KeyCode.V;
        public KeyCode interactKey = KeyCode.E;
        
        [Header("Audio")]
        public AudioClip[] footstepSounds;
        public AudioClip jumpSound;
        public AudioClip landSound;
        
        private CharacterController controller;
        private Vector3 velocity;
        private bool isGrounded;
        private bool isRunning;
        private AudioSource audioSource;
        private float footstepTimer;
        
        // Parkour system
        private bool canVault = false;
        private bool canClimb = false;
        private Vector3 vaultTarget;
        private Vector3 climbTarget;
        
        private void Start()
        {
            controller = GetComponent<CharacterController>();
            audioSource = GetComponent<AudioSource>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            // Set player tag for identification
            gameObject.tag = "Player";
        }
        
        private void Update()
        {
            HandleInput();
            HandleMovement();
            HandleParkour();
            UpdateAudio();
        }
        
        private void HandleInput()
        {
            isRunning = Input.GetKey(runKey);
            
            if (Input.GetKeyDown(jumpKey))
            {
                if (isGrounded)
                {
                    Jump();
                }
                else if (canVault)
                {
                    PerformVault();
                }
                else if (canClimb)
                {
                    PerformClimb();
                }
            }
            
            if (Input.GetKeyDown(vaultKey) && canVault)
            {
                PerformVault();
            }
        }
        
        private void HandleMovement()
        {
            // Ground check
            isGrounded = controller.isGrounded;
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f; // Small downward force to keep grounded
            }
            
            // Get input
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            // Calculate movement direction
            Vector3 direction = new Vector3(horizontal, 0f, vertical).normalized;
            
            if (direction.magnitude >= 0.1f)
            {
                // Calculate target angle based on camera
                Camera mainCamera = Camera.main;
                if (mainCamera != null)
                {
                    float targetAngle = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg + mainCamera.transform.eulerAngles.y;
                    float angle = Mathf.SmoothDampAngle(transform.eulerAngles.y, targetAngle, ref velocity.y, 0.1f);
                    transform.rotation = Quaternion.AngleAxis(angle, Vector3.up);
                    
                    Vector3 moveDirection = Quaternion.AngleAxis(targetAngle, Vector3.up) * Vector3.forward;
                    float currentSpeed = isRunning ? runSpeed : walkSpeed;
                    controller.Move(moveDirection.normalized * currentSpeed * Time.deltaTime);
                }
            }
            
            // Apply gravity
            velocity.y += gravity * Time.deltaTime;
            controller.Move(velocity * Time.deltaTime);
        }
        
        private void HandleParkour()
        {
            // Check for parkour opportunities
            CheckForVault();
            CheckForClimb();
        }
        
        private void CheckForVault()
        {
            Vector3 rayOrigin = transform.position + Vector3.up * 0.5f;
            Vector3 rayDirection = transform.forward;
            
            RaycastHit hit;
            if (Physics.Raycast(rayOrigin, rayDirection, out hit, parkourRange, parkourLayers))
            {
                if (hit.point.y - transform.position.y <= vaultHeight && hit.point.y - transform.position.y > 0.3f)
                {
                    canVault = true;
                    vaultTarget = hit.point + hit.normal * 0.5f + Vector3.up * 0.5f;
                }
                else
                {
                    canVault = false;
                }
            }
            else
            {
                canVault = false;
            }
        }
        
        private void CheckForClimb()
        {
            Vector3 rayOrigin = transform.position + Vector3.up * 1.5f;
            Vector3 rayDirection = transform.forward;
            
            RaycastHit hit;
            if (Physics.Raycast(rayOrigin, rayDirection, out hit, parkourRange, parkourLayers))
            {
                if (hit.point.y - transform.position.y <= climbHeight && hit.point.y - transform.position.y > vaultHeight)
                {
                    canClimb = true;
                    climbTarget = hit.point + hit.normal * 0.5f + Vector3.up * 0.5f;
                }
                else
                {
                    canClimb = false;
                }
            }
            else
            {
                canClimb = false;
            }
        }
        
        private void Jump()
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            PlaySound(jumpSound);
        }
        
        private void PerformVault()
        {
            if (!canVault) return;
            
            // Simple vault - teleport to vault target
            // In a full implementation, this would be a smooth animation
            controller.enabled = false;
            transform.position = vaultTarget;
            controller.enabled = true;
            
            velocity.y = 0f;
            canVault = false;
            
            Debug.Log("Performed vault!");
        }
        
        private void PerformClimb()
        {
            if (!canClimb) return;
            
            // Simple climb - teleport to climb target
            // In a full implementation, this would be a smooth animation
            controller.enabled = false;
            transform.position = climbTarget;
            controller.enabled = true;
            
            velocity.y = 0f;
            canClimb = false;
            
            Debug.Log("Performed climb!");
        }
        
        private void UpdateAudio()
        {
            // Footstep sounds
            if (isGrounded && controller.velocity.magnitude > 0.1f)
            {
                footstepTimer += Time.deltaTime;
                float footstepInterval = isRunning ? 0.3f : 0.5f;
                
                if (footstepTimer >= footstepInterval)
                {
                    PlayFootstepSound();
                    footstepTimer = 0f;
                }
            }
            else
            {
                footstepTimer = 0f;
            }
        }
        
        private void PlayFootstepSound()
        {
            if (footstepSounds.Length > 0)
            {
                AudioClip clip = footstepSounds[Random.Range(0, footstepSounds.Length)];
                PlaySound(clip);
            }
        }
        
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        
        private void OnControllerColliderHit(ControllerColliderHit hit)
        {
            // Handle landing sound
            if (isGrounded && velocity.y < -5f)
            {
                PlaySound(landSound);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw parkour detection ranges
            Gizmos.color = Color.blue;
            Vector3 rayOrigin = transform.position + Vector3.up * 0.5f;
            Gizmos.DrawRay(rayOrigin, transform.forward * parkourRange);
            
            Gizmos.color = Color.green;
            Vector3 climbRayOrigin = transform.position + Vector3.up * 1.5f;
            Gizmos.DrawRay(climbRayOrigin, transform.forward * parkourRange);
            
            // Draw vault and climb targets
            if (canVault)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawSphere(vaultTarget, 0.3f);
            }
            
            if (canClimb)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawSphere(climbTarget, 0.3f);
            }
        }
    }
}
