using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages all drug-related operations including production, inventory, and quality
    /// </summary>
    public class DrugManager : MonoBehaviour
    {
        [Header("Drug Database")]
        public DrugDatabase drugDatabase;
        
        [Header("Production Settings")]
        public float baseProductionTime = 60f;
        public float qualityVariance = 0.1f;
        
        // Current inventory
        private Dictionary<DrugType, List<DrugBatch>> inventory = new Dictionary<DrugType, List<DrugBatch>>();
        
        // Production queues
        private List<ProductionOrder> activeProduction = new List<ProductionOrder>();
        
        // Events
        public System.Action<DrugType, int> OnDrugProduced;
        public System.Action<DrugType, int> OnDrugSold;
        public System.Action<ProductionOrder> OnProductionStarted;
        public System.Action<ProductionOrder> OnProductionCompleted;
        
        private void Start()
        {
            InitializeInventory();
        }
        
        private void Update()
        {
            UpdateProduction();
        }
        
        private void InitializeInventory()
        {
            // Initialize inventory for all drug types
            foreach (DrugType drugType in System.Enum.GetValues(typeof(DrugType)))
            {
                inventory[drugType] = new List<DrugBatch>();
            }
        }
        
        private void UpdateProduction()
        {
            for (int i = activeProduction.Count - 1; i >= 0; i--)
            {
                var production = activeProduction[i];
                production.remainingTime -= Time.deltaTime;
                
                if (production.remainingTime <= 0)
                {
                    CompleteProduction(production);
                    activeProduction.RemoveAt(i);
                }
            }
        }
        
        public bool StartProduction(DrugType drugType, int quantity, List<Ingredient> ingredients = null)
        {
            var drugData = drugDatabase.GetDrugData(drugType);
            if (drugData == null)
            {
                Debug.LogError($"Drug data not found for {drugType}");
                return false;
            }
            
            // Check if we have required ingredients
            if (!HasRequiredIngredients(drugData.requiredIngredients))
            {
                Debug.Log("Insufficient ingredients for production");
                return false;
            }
            
            // Calculate production time based on quantity and drug complexity
            float productionTime = baseProductionTime * quantity * drugData.complexityMultiplier;
            
            // Create production order
            var order = new ProductionOrder
            {
                drugType = drugType,
                quantity = quantity,
                totalTime = productionTime,
                remainingTime = productionTime,
                ingredients = ingredients ?? drugData.requiredIngredients,
                startTime = Time.time
            };
            
            activeProduction.Add(order);
            OnProductionStarted?.Invoke(order);
            
            Debug.Log($"Started production of {quantity} {drugType} (ETA: {productionTime:F1}s)");
            return true;
        }
        
        private void CompleteProduction(ProductionOrder order)
        {
            // Calculate quality based on ingredients and random variance
            float quality = CalculateQuality(order.ingredients);
            
            // Create drug batch
            var batch = new DrugBatch
            {
                drugType = order.drugType,
                quantity = order.quantity,
                quality = quality,
                purity = CalculatePurity(order.ingredients),
                productionDate = System.DateTime.Now,
                ingredients = order.ingredients
            };
            
            // Add to inventory
            inventory[order.drugType].Add(batch);
            
            OnProductionCompleted?.Invoke(order);
            OnDrugProduced?.Invoke(order.drugType, order.quantity);
            
            Debug.Log($"Completed production: {order.quantity} {order.drugType} (Quality: {quality:F2})");
        }
        
        private float CalculateQuality(List<Ingredient> ingredients)
        {
            float baseQuality = 0.5f;
            
            foreach (var ingredient in ingredients)
            {
                baseQuality += ingredient.qualityBonus;
            }
            
            // Add random variance
            baseQuality += Random.Range(-qualityVariance, qualityVariance);
            
            return Mathf.Clamp01(baseQuality);
        }
        
        private float CalculatePurity(List<Ingredient> ingredients)
        {
            float basePurity = 0.7f;
            
            foreach (var ingredient in ingredients)
            {
                basePurity += ingredient.purityBonus;
            }
            
            return Mathf.Clamp01(basePurity);
        }
        
        private bool HasRequiredIngredients(List<Ingredient> required)
        {
            // Simplified check - in full implementation, check actual ingredient inventory
            return true;
        }
        
        public List<DrugBatch> GetInventory(DrugType drugType)
        {
            return inventory.ContainsKey(drugType) ? inventory[drugType] : new List<DrugBatch>();
        }
        
        public int GetTotalQuantity(DrugType drugType)
        {
            return GetInventory(drugType).Sum(batch => batch.quantity);
        }
        
        public bool SellDrugs(DrugType drugType, int quantity)
        {
            var batches = GetInventory(drugType);
            int remaining = quantity;
            
            for (int i = batches.Count - 1; i >= 0 && remaining > 0; i--)
            {
                var batch = batches[i];
                int toSell = Mathf.Min(batch.quantity, remaining);
                
                batch.quantity -= toSell;
                remaining -= toSell;
                
                if (batch.quantity <= 0)
                {
                    batches.RemoveAt(i);
                }
            }
            
            if (remaining == 0)
            {
                OnDrugSold?.Invoke(drugType, quantity);
                return true;
            }
            
            return false;
        }
        
        public float GetAverageQuality(DrugType drugType)
        {
            var batches = GetInventory(drugType);
            if (batches.Count == 0) return 0f;
            
            float totalQuality = 0f;
            int totalQuantity = 0;
            
            foreach (var batch in batches)
            {
                totalQuality += batch.quality * batch.quantity;
                totalQuantity += batch.quantity;
            }
            
            return totalQuantity > 0 ? totalQuality / totalQuantity : 0f;
        }
    }
}
