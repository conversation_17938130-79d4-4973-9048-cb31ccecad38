using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages the fishing mini-game system with hidden rewards
    /// </summary>
    public class FishingManager : MonoBehaviour
    {
        [Header("Fishing Settings")]
        public float fishingDuration = 30f;
        public float catchChance = 0.3f;
        public float rareItemChance = 0.05f;
        
        [Header("Fishing Locations")]
        public List<FishingSpot> fishingSpots = new List<FishingSpot>();
        
        // Current fishing state
        private bool isFishing = false;
        private float fishingTimer = 0f;
        private FishingSpot currentSpot;
        private FishingSession currentSession;
        
        // Events
        public System.Action<FishingSession> OnFishingStarted;
        public System.Action<FishingCatch> OnFishCaught;
        public System.Action<FishingSession> OnFishingCompleted;
        
        private void Start()
        {
            InitializeFishingSpots();
        }
        
        private void Update()
        {
            if (isFishing)
            {
                UpdateFishing();
            }
        }
        
        private void InitializeFishingSpots()
        {
            // Create default fishing spots
            fishingSpots.Add(new FishingSpot
            {
                name = "City Pier",
                location = new Vector3(100f, 0f, 200f),
                fishQuality = 0.3f,
                rareItemChance = 0.02f,
                isUnlocked = true
            });
            
            fishingSpots.Add(new FishingSpot
            {
                name = "Industrial Harbor",
                location = new Vector3(-150f, 0f, 300f),
                fishQuality = 0.2f,
                rareItemChance = 0.08f, // Higher chance for contraband
                isUnlocked = false
            });
            
            fishingSpots.Add(new FishingSpot
            {
                name = "Private Lake",
                location = new Vector3(500f, 0f, -200f),
                fishQuality = 0.8f,
                rareItemChance = 0.15f,
                isUnlocked = false
            });
        }
        
        public bool StartFishing(string spotName)
        {
            if (isFishing)
            {
                Debug.Log("Already fishing");
                return false;
            }
            
            var spot = fishingSpots.Find(s => s.name == spotName);
            if (spot == null || !spot.isUnlocked)
            {
                Debug.Log("Fishing spot not available");
                return false;
            }
            
            currentSpot = spot;
            currentSession = new FishingSession
            {
                spotName = spotName,
                startTime = Time.time,
                catches = new List<FishingCatch>()
            };
            
            isFishing = true;
            fishingTimer = 0f;
            
            OnFishingStarted?.Invoke(currentSession);
            Debug.Log($"Started fishing at {spotName}");
            
            return true;
        }
        
        public void StopFishing()
        {
            if (!isFishing) return;
            
            isFishing = false;
            currentSession.endTime = Time.time;
            currentSession.duration = currentSession.endTime - currentSession.startTime;
            
            OnFishingCompleted?.Invoke(currentSession);
            Debug.Log($"Fishing session completed. Caught {currentSession.catches.Count} items");
            
            currentSession = null;
            currentSpot = null;
        }
        
        private void UpdateFishing()
        {
            fishingTimer += Time.deltaTime;
            
            // Check for catches every few seconds
            if (fishingTimer >= 5f) // Check every 5 seconds
            {
                fishingTimer = 0f;
                AttemptCatch();
            }
            
            // Auto-stop after duration
            if (currentSession != null && Time.time - currentSession.startTime >= fishingDuration)
            {
                StopFishing();
            }
        }
        
        private void AttemptCatch()
        {
            if (Random.value < catchChance)
            {
                var fishingCatch = GenerateCatch();
                currentSession.catches.Add(fishingCatch);
                ProcessCatch(fishingCatch);
                OnFishCaught?.Invoke(fishingCatch);
            }
        }
        
        private FishingCatch GenerateCatch()
        {
            var fishingCatch = new FishingCatch
            {
                timestamp = Time.time,
                location = currentSpot.name
            };
            
            // Determine what was caught
            float rareRoll = Random.value;
            
            if (rareRoll < currentSpot.rareItemChance)
            {
                // Rare item caught
                fishingCatch.type = FishingCatchType.RareItem;
                fishingCatch.item = GenerateRareItem();
                fishingCatch.value = CalculateRareItemValue(fishingCatch.item);
            }
            else if (rareRoll < currentSpot.rareItemChance + 0.1f)
            {
                // Junk item
                fishingCatch.type = FishingCatchType.Junk;
                fishingCatch.item = GenerateJunkItem();
                fishingCatch.value = Random.Range(1f, 10f);
            }
            else
            {
                // Regular fish
                fishingCatch.type = FishingCatchType.Fish;
                fishingCatch.item = GenerateFish();
                fishingCatch.value = CalculateFishValue();
            }
            
            return fishingCatch;
        }
        
        private string GenerateRareItem()
        {
            string[] rareItems = {
                "Waterproof Drug Package",
                "Gold Bar",
                "Diamond Ring",
                "USB Drive with Intel",
                "Briefcase with Cash",
                "Encrypted Phone",
                "Chemical Sample",
                "Police Evidence Bag",
                "Cartel Medallion",
                "Vintage Wine Bottle"
            };
            
            return rareItems[Random.Range(0, rareItems.Length)];
        }
        
        private string GenerateJunkItem()
        {
            string[] junkItems = {
                "Old Boot",
                "Rusty Can",
                "Plastic Bottle",
                "Broken Phone",
                "Car Tire",
                "Shopping Cart",
                "Traffic Cone",
                "License Plate"
            };
            
            return junkItems[Random.Range(0, junkItems.Length)];
        }
        
        private string GenerateFish()
        {
            string[] fishTypes = {
                "Bass",
                "Trout",
                "Salmon",
                "Catfish",
                "Pike",
                "Carp",
                "Perch",
                "Tuna"
            };
            
            return fishTypes[Random.Range(0, fishTypes.Length)];
        }
        
        private float CalculateRareItemValue(string item)
        {
            switch (item)
            {
                case "Waterproof Drug Package": return Random.Range(5000f, 15000f);
                case "Gold Bar": return Random.Range(10000f, 25000f);
                case "Diamond Ring": return Random.Range(8000f, 20000f);
                case "USB Drive with Intel": return Random.Range(3000f, 8000f);
                case "Briefcase with Cash": return Random.Range(15000f, 50000f);
                case "Encrypted Phone": return Random.Range(2000f, 5000f);
                case "Chemical Sample": return Random.Range(1000f, 3000f);
                case "Police Evidence Bag": return Random.Range(500f, 2000f);
                case "Cartel Medallion": return Random.Range(5000f, 12000f);
                case "Vintage Wine Bottle": return Random.Range(2000f, 8000f);
                default: return Random.Range(100f, 1000f);
            }
        }
        
        private float CalculateFishValue()
        {
            float baseValue = Random.Range(10f, 50f);
            float qualityMultiplier = currentSpot.fishQuality;
            return baseValue * qualityMultiplier;
        }
        
        private void ProcessCatch(FishingCatch fishingCatch)
        {
            switch (fishingCatch.type)
            {
                case FishingCatchType.Fish:
                    // Sell fish for clean money
                    GameManager.Instance.moneyManager.AddCleanMoney(fishingCatch.value, $"Sold {fishingCatch.item}");
                    break;
                    
                case FishingCatchType.RareItem:
                    ProcessRareItem(fishingCatch);
                    break;
                    
                case FishingCatchType.Junk:
                    // Small clean money from recycling
                    GameManager.Instance.moneyManager.AddCleanMoney(fishingCatch.value, $"Recycled {fishingCatch.item}");
                    break;
            }
        }
        
        private void ProcessRareItem(FishingCatch fishingCatch)
        {
            switch (fishingCatch.item)
            {
                case "Waterproof Drug Package":
                    // Add random drugs to inventory
                    var drugType = (DrugType)Random.Range(0, System.Enum.GetValues(typeof(DrugType)).Length);
                    // GameManager.Instance.drugManager.AddDrugs(drugType, Random.Range(10, 50));
                    break;
                    
                case "Briefcase with Cash":
                    // Add dirty money
                    GameManager.Instance.moneyManager.AddDirtyMoney(fishingCatch.value, "Found briefcase");
                    break;
                    
                case "USB Drive with Intel":
                    // Reduce police heat
                    GameManager.Instance.policeManager.AddHeat(-20f, "Intel from USB drive");
                    break;
                    
                case "Police Evidence Bag":
                    // Increase police heat
                    GameManager.Instance.policeManager.AddHeat(15f, "Found police evidence");
                    break;
                    
                default:
                    // Convert to clean money
                    GameManager.Instance.moneyManager.AddCleanMoney(fishingCatch.value, $"Sold {fishingCatch.item}");
                    break;
            }
        }
        
        public void UnlockFishingSpot(string spotName)
        {
            var spot = fishingSpots.Find(s => s.name == spotName);
            if (spot != null)
            {
                spot.isUnlocked = true;
                Debug.Log($"Unlocked fishing spot: {spotName}");
            }
        }
        
        public List<FishingSpot> GetAvailableSpots()
        {
            return fishingSpots.FindAll(s => s.isUnlocked);
        }
        
        public bool IsFishing() => isFishing;
        public FishingSession GetCurrentSession() => currentSession;
    }
    
    [System.Serializable]
    public class FishingSpot
    {
        public string name;
        public Vector3 location;
        public float fishQuality; // 0-1, affects fish value
        public float rareItemChance; // 0-1, chance for special items
        public bool isUnlocked;
        public string description;
        
        public string GetDescription()
        {
            if (!string.IsNullOrEmpty(description)) return description;
            
            switch (name)
            {
                case "City Pier":
                    return "Popular fishing spot with decent catches and low risk";
                case "Industrial Harbor":
                    return "Polluted waters but sometimes contains valuable contraband";
                case "Private Lake":
                    return "Exclusive location with premium fish and rare finds";
                default:
                    return "A fishing location";
            }
        }
    }
    
    [System.Serializable]
    public class FishingSession
    {
        public string spotName;
        public float startTime;
        public float endTime;
        public float duration;
        public List<FishingCatch> catches;
        
        public float GetTotalValue()
        {
            float total = 0f;
            foreach (var fishingCatch in catches)
            {
                total += fishingCatch.value;
            }
            return total;
        }
    }
    
    [System.Serializable]
    public class FishingCatch
    {
        public FishingCatchType type;
        public string item;
        public float value;
        public float timestamp;
        public string location;
    }
    
    public enum FishingCatchType
    {
        Fish,
        RareItem,
        Junk
    }
}
