using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Minimal player controller that works without any dependencies
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    public class MinimalPlayerController : MonoBehaviour
    {
        [Header("Movement Settings")]
        public float walkSpeed = 5f;
        public float runSpeed = 10f;
        public float jumpHeight = 2f;
        public float gravity = -9.81f;
        
        private CharacterController controller;
        private Vector3 velocity;
        private bool isGrounded;
        
        private void Start()
        {
            controller = GetComponent<CharacterController>();
            gameObject.tag = "Player";
            Debug.Log("Minimal Player Controller initialized. Use WASD to move, Shift to run, E to sell drugs.");
        }
        
        private void Update()
        {
            HandleMovement();
            HandleActions();
        }
        
        private void HandleMovement()
        {
            // Ground check
            isGrounded = controller.isGrounded;
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f;
            }
            
            // Get input
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            bool isRunning = Input.GetKey(KeyCode.LeftShift);
            
            // Calculate movement
            Vector3 direction = new Vector3(horizontal, 0f, vertical).normalized;
            
            if (direction.magnitude >= 0.1f)
            {
                float currentSpeed = isRunning ? runSpeed : walkSpeed;
                controller.Move(direction * currentSpeed * Time.deltaTime);
                
                // Rotate to face movement direction
                transform.rotation = Quaternion.LookRotation(direction);
            }
            
            // Jump
            if (Input.GetKeyDown(KeyCode.Space) && isGrounded)
            {
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
                Debug.Log("Player jumped!");
            }
            
            // Apply gravity
            velocity.y += gravity * Time.deltaTime;
            controller.Move(velocity * Time.deltaTime);
        }
        
        private void HandleActions()
        {
            // Drug dealing action
            if (Input.GetKeyDown(KeyCode.E))
            {
                var gameManager = MinimalGameManager.Instance;
                if (gameManager != null)
                {
                    float saleAmount = Random.Range(50f, 200f);
                    gameManager.SellDrugs(saleAmount);
                }
            }
        }
    }
}
