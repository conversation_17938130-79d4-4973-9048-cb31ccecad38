using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages property ownership, customization, and operations
    /// </summary>
    public class PropertyManager : MonoBehaviour
    {
        [Header("Property Settings")]
        public int maxProperties = 10;
        public float propertyMaintenanceCost = 100f;
        
        // Owned properties
        private List<Property> ownedProperties = new List<Property>();
        private List<PropertyListing> availableProperties = new List<PropertyListing>();
        
        // Events
        public System.Action<Property> OnPropertyPurchased;
        public System.Action<Property> OnPropertySold;
        public System.Action<Property> OnPropertyUpgraded;
        public System.Action<PropertyListing> OnNewPropertyAvailable;
        
        private void Start()
        {
            InitializePropertySystem();
            GenerateInitialProperties();
        }
        
        private void Update()
        {
            UpdateProperties();
            GenerateNewProperties();
        }
        
        private void InitializePropertySystem()
        {
            // Player starts with a basic safe house
            var starterProperty = new Property
            {
                id = System.Guid.NewGuid().ToString(),
                name = "Abandoned Warehouse",
                type = PropertyType.SafeHouse,
                location = Vector3.zero,
                purchasePrice = 0f, // Free starter
                currentValue = 25000f,
                securityLevel = 0.2f,
                capacity = 50f,
                isOwned = true,
                purchaseDate = System.DateTime.Now
            };
            
            ownedProperties.Add(starterProperty);
            OnPropertyPurchased?.Invoke(starterProperty);
        }
        
        private void GenerateInitialProperties()
        {
            // Generate some initial properties for sale
            for (int i = 0; i < 5; i++)
            {
                GenerateRandomProperty();
            }
        }
        
        private void GenerateRandomProperty()
        {
            var propertyTypes = System.Enum.GetValues(typeof(PropertyType));
            var randomType = (PropertyType)propertyTypes.GetValue(Random.Range(0, propertyTypes.Length));
            
            var listing = new PropertyListing
            {
                id = System.Guid.NewGuid().ToString(),
                name = GeneratePropertyName(randomType),
                type = randomType,
                location = GenerateRandomLocation(),
                price = CalculatePropertyPrice(randomType),
                securityLevel = Random.Range(0.1f, 0.9f),
                capacity = CalculatePropertyCapacity(randomType),
                description = GeneratePropertyDescription(randomType),
                isAvailable = true,
                listingDate = System.DateTime.Now
            };
            
            availableProperties.Add(listing);
            OnNewPropertyAvailable?.Invoke(listing);
        }
        
        private void UpdateProperties()
        {
            foreach (var property in ownedProperties)
            {
                // Generate passive income for businesses
                if (property.type == PropertyType.Business)
                {
                    UpdateBusinessIncome(property);
                }
                
                // Degrade security over time without maintenance
                if (Time.time - property.lastMaintenance >= 604800f) // 7 days
                {
                    property.securityLevel = Mathf.Max(0.1f, property.securityLevel - 0.01f);
                }
                
                // Update property value based on area and improvements
                UpdatePropertyValue(property);
            }
        }
        
        private void UpdateBusinessIncome(Property property)
        {
            if (Time.time - property.lastIncomeGenerated >= 3600f) // Every hour
            {
                float income = CalculateBusinessIncome(property);
                GameManager.Instance.moneyManager.AddCleanMoney(income, $"Income from {property.name}");
                property.lastIncomeGenerated = Time.time;
            }
        }
        
        private float CalculateBusinessIncome(Property property)
        {
            float baseIncome = 100f;
            float securityBonus = property.securityLevel * 50f;
            float upgradeBonus = property.upgrades.Count * 25f;
            
            return baseIncome + securityBonus + upgradeBonus;
        }
        
        private void UpdatePropertyValue(Property property)
        {
            // Property value increases with upgrades and security
            float upgradeValue = property.upgrades.Sum(u => u.cost) * 0.8f; // 80% of upgrade cost
            float securityValue = property.securityLevel * 10000f;
            
            property.currentValue = property.purchasePrice + upgradeValue + securityValue;
        }
        
        public bool PurchaseProperty(string listingId)
        {
            var listing = availableProperties.FirstOrDefault(p => p.id == listingId);
            if (listing == null || !listing.isAvailable)
            {
                Debug.Log("Property not available");
                return false;
            }
            
            if (ownedProperties.Count >= maxProperties)
            {
                Debug.Log("Maximum properties owned");
                return false;
            }
            
            var moneyManager = GameManager.Instance.moneyManager;
            if (!moneyManager.SpendCleanMoney(listing.price, $"Purchase {listing.name}"))
            {
                Debug.Log("Insufficient funds");
                return false;
            }
            
            // Convert listing to owned property
            var property = new Property
            {
                id = listing.id,
                name = listing.name,
                type = listing.type,
                location = listing.location,
                purchasePrice = listing.price,
                currentValue = listing.price,
                securityLevel = listing.securityLevel,
                capacity = listing.capacity,
                isOwned = true,
                purchaseDate = System.DateTime.Now,
                lastMaintenance = Time.time,
                lastIncomeGenerated = Time.time,
                upgrades = new List<PropertyUpgrade>()
            };
            
            ownedProperties.Add(property);
            availableProperties.Remove(listing);
            
            OnPropertyPurchased?.Invoke(property);
            Debug.Log($"Purchased property: {property.name} for ${listing.price:F0}");
            
            return true;
        }
        
        public bool SellProperty(string propertyId)
        {
            var property = ownedProperties.FirstOrDefault(p => p.id == propertyId);
            if (property == null)
            {
                return false;
            }
            
            // Can't sell starter property
            if (property.purchasePrice == 0f)
            {
                Debug.Log("Cannot sell starter property");
                return false;
            }
            
            // Sell for 80% of current value
            float salePrice = property.currentValue * 0.8f;
            GameManager.Instance.moneyManager.AddCleanMoney(salePrice, $"Sold {property.name}");
            
            ownedProperties.Remove(property);
            OnPropertySold?.Invoke(property);
            
            Debug.Log($"Sold property: {property.name} for ${salePrice:F0}");
            return true;
        }
        
        public bool UpgradeProperty(string propertyId, PropertyUpgradeType upgradeType)
        {
            var property = ownedProperties.FirstOrDefault(p => p.id == propertyId);
            if (property == null)
            {
                return false;
            }
            
            // Check if upgrade already exists
            if (property.upgrades.Any(u => u.type == upgradeType))
            {
                Debug.Log("Upgrade already installed");
                return false;
            }
            
            var upgradeData = GetUpgradeData(upgradeType);
            if (upgradeData == null)
            {
                return false;
            }
            
            var moneyManager = GameManager.Instance.moneyManager;
            if (!moneyManager.SpendCleanMoney(upgradeData.cost, $"Upgrade {property.name}"))
            {
                return false;
            }
            
            var upgrade = new PropertyUpgrade
            {
                type = upgradeType,
                name = upgradeData.name,
                cost = upgradeData.cost,
                securityBonus = upgradeData.securityBonus,
                capacityBonus = upgradeData.capacityBonus,
                installDate = System.DateTime.Now
            };
            
            property.upgrades.Add(upgrade);
            property.securityLevel += upgrade.securityBonus;
            property.capacity += upgrade.capacityBonus;
            
            OnPropertyUpgraded?.Invoke(property);
            Debug.Log($"Upgraded {property.name} with {upgrade.name}");
            
            return true;
        }
        
        public bool PerformMaintenance(string propertyId)
        {
            var property = ownedProperties.FirstOrDefault(p => p.id == propertyId);
            if (property == null)
            {
                return false;
            }
            
            var moneyManager = GameManager.Instance.moneyManager;
            if (!moneyManager.SpendCleanMoney(propertyMaintenanceCost, $"Maintenance for {property.name}"))
            {
                return false;
            }
            
            property.lastMaintenance = Time.time;
            property.securityLevel = Mathf.Min(1f, property.securityLevel + 0.1f);
            
            Debug.Log($"Performed maintenance on {property.name}");
            return true;
        }
        
        private void GenerateNewProperties()
        {
            // Generate new properties occasionally
            if (Random.value < 0.001f && availableProperties.Count < 10) // Low chance per frame
            {
                GenerateRandomProperty();
            }
        }
        
        private string GeneratePropertyName(PropertyType type)
        {
            string[] prefixes = { "Sunset", "Golden", "Silver", "Blue", "Green", "Red", "Crystal", "Diamond" };
            string[] suffixes = { "Heights", "Plaza", "Gardens", "Tower", "Complex", "Center", "Point", "View" };
            
            switch (type)
            {
                case PropertyType.SafeHouse:
                    return $"Safe House #{Random.Range(1, 100)}";
                case PropertyType.Laboratory:
                    return $"Lab Facility {Random.Range(1, 50)}";
                case PropertyType.Warehouse:
                    return $"{prefixes[Random.Range(0, prefixes.Length)]} Warehouse";
                case PropertyType.Business:
                    return $"{prefixes[Random.Range(0, prefixes.Length)]} {suffixes[Random.Range(0, suffixes.Length)]}";
                default:
                    return "Unknown Property";
            }
        }
        
        private Vector3 GenerateRandomLocation()
        {
            return new Vector3(Random.Range(-1000f, 1000f), 0f, Random.Range(-1000f, 1000f));
        }
        
        private float CalculatePropertyPrice(PropertyType type)
        {
            switch (type)
            {
                case PropertyType.SafeHouse: return Random.Range(25000f, 75000f);
                case PropertyType.Laboratory: return Random.Range(100000f, 300000f);
                case PropertyType.Warehouse: return Random.Range(150000f, 400000f);
                case PropertyType.Business: return Random.Range(200000f, 500000f);
                default: return 50000f;
            }
        }
        
        private float CalculatePropertyCapacity(PropertyType type)
        {
            switch (type)
            {
                case PropertyType.SafeHouse: return Random.Range(20f, 100f);
                case PropertyType.Laboratory: return Random.Range(50f, 200f);
                case PropertyType.Warehouse: return Random.Range(200f, 1000f);
                case PropertyType.Business: return Random.Range(30f, 150f);
                default: return 50f;
            }
        }
        
        private string GeneratePropertyDescription(PropertyType type)
        {
            switch (type)
            {
                case PropertyType.SafeHouse:
                    return "Secure location for storing contraband and planning operations";
                case PropertyType.Laboratory:
                    return "Equipped facility for drug production and chemical processing";
                case PropertyType.Warehouse:
                    return "Large storage facility for bulk operations and distribution";
                case PropertyType.Business:
                    return "Legitimate business front for money laundering operations";
                default:
                    return "Property description not available";
            }
        }
        
        private PropertyUpgradeData GetUpgradeData(PropertyUpgradeType type)
        {
            // In full implementation, this would come from a ScriptableObject
            switch (type)
            {
                case PropertyUpgradeType.SecuritySystem:
                    return new PropertyUpgradeData("Security System", 15000f, 0.2f, 0f);
                case PropertyUpgradeType.HiddenCompartments:
                    return new PropertyUpgradeData("Hidden Compartments", 25000f, 0.1f, 50f);
                case PropertyUpgradeType.AdvancedEquipment:
                    return new PropertyUpgradeData("Advanced Equipment", 50000f, 0f, 100f);
                case PropertyUpgradeType.EscapeRoute:
                    return new PropertyUpgradeData("Escape Route", 30000f, 0.3f, 0f);
                default:
                    return null;
            }
        }
        
        // Getters
        public List<Property> GetOwnedProperties() => new List<Property>(ownedProperties);
        public List<PropertyListing> GetAvailableProperties() => new List<PropertyListing>(availableProperties);
        public Property GetProperty(string id) => ownedProperties.FirstOrDefault(p => p.id == id);
    }
}
