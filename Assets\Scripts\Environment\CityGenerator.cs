using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Generates the 3D city environment with buildings, roads, and landmarks
    /// </summary>
    public class CityGenerator : MonoBehaviour
    {
        [Header("City Settings")]
        public int citySize = 50;
        public float blockSize = 20f;
        public float roadWidth = 4f;
        
        [Header("Building Prefabs")]
        public GameObject[] residentialBuildings;
        public GameObject[] commercialBuildings;
        public GameObject[] industrialBuildings;
        public GameObject[] specialBuildings;
        
        [Header("Environment Prefabs")]
        public GameObject roadPrefab;
        public GameObject sidewalkPrefab;
        public GameObject streetLightPrefab;
        public GameObject treePrefab;
        
        [Header("Territory Markers")]
        public GameObject territoryMarkerPrefab;
        public Material playerTerritoryMaterial;
        public Material enemyTerritoryMaterial;
        public Material neutralTerritoryMaterial;
        
        private Dictionary<Vector2Int, CityBlock> cityBlocks = new Dictionary<Vector2Int, CityBlock>();
        private List<GameObject> territoryMarkers = new List<GameObject>();
        
        private void Start()
        {
            GenerateCity();
            CreateTerritoryMarkers();
        }
        
        private void GenerateCity()
        {
            // Generate city grid
            for (int x = -citySize / 2; x < citySize / 2; x++)
            {
                for (int z = -citySize / 2; z < citySize / 2; z++)
                {
                    Vector2Int blockCoord = new Vector2Int(x, z);
                    Vector3 blockPosition = new Vector3(x * blockSize, 0, z * blockSize);
                    
                    CityBlock block = GenerateBlock(blockCoord, blockPosition);
                    cityBlocks[blockCoord] = block;
                }
            }
            
            // Generate roads
            GenerateRoads();
            
            // Add environmental details
            AddEnvironmentalDetails();
        }
        
        private CityBlock GenerateBlock(Vector2Int coord, Vector3 position)
        {
            CityBlock block = new CityBlock
            {
                coordinate = coord,
                position = position,
                blockType = DetermineBlockType(coord),
                buildings = new List<GameObject>()
            };
            
            // Create buildings based on block type
            int buildingCount = Random.Range(1, 4);
            for (int i = 0; i < buildingCount; i++)
            {
                GameObject building = CreateBuilding(block.blockType, position, i);
                if (building != null)
                {
                    block.buildings.Add(building);
                }
            }
            
            return block;
        }
        
        private BlockType DetermineBlockType(Vector2Int coord)
        {
            float distanceFromCenter = Vector2.Distance(coord, Vector2.zero);
            
            if (distanceFromCenter < 5f)
            {
                return BlockType.Commercial; // Downtown area
            }
            else if (distanceFromCenter < 15f)
            {
                return Random.value < 0.7f ? BlockType.Residential : BlockType.Commercial;
            }
            else
            {
                return Random.value < 0.6f ? BlockType.Residential : BlockType.Industrial;
            }
        }
        
        private GameObject CreateBuilding(BlockType blockType, Vector3 blockPosition, int buildingIndex)
        {
            GameObject[] prefabArray = GetBuildingPrefabs(blockType);
            if (prefabArray == null || prefabArray.Length == 0) return null;
            
            GameObject prefab = prefabArray[Random.Range(0, prefabArray.Length)];
            if (prefab == null) return null;
            
            // Calculate building position within block
            Vector3 offset = new Vector3(
                Random.Range(-blockSize / 3f, blockSize / 3f),
                0,
                Random.Range(-blockSize / 3f, blockSize / 3f)
            );
            
            Vector3 buildingPosition = blockPosition + offset;
            Quaternion buildingRotation = Quaternion.Euler(0, Random.Range(0, 4) * 90f, 0);
            
            GameObject building = Instantiate(prefab, buildingPosition, buildingRotation);
            building.name = $"{blockType}_Building_{buildingIndex}";
            
            // Add building component for interaction
            BuildingInteraction buildingInteraction = building.GetComponent<BuildingInteraction>();
            if (buildingInteraction == null)
            {
                buildingInteraction = building.AddComponent<BuildingInteraction>();
            }
            
            buildingInteraction.blockType = blockType;
            buildingInteraction.buildingPosition = buildingPosition;
            
            return building;
        }
        
        private GameObject[] GetBuildingPrefabs(BlockType blockType)
        {
            switch (blockType)
            {
                case BlockType.Residential: return residentialBuildings;
                case BlockType.Commercial: return commercialBuildings;
                case BlockType.Industrial: return industrialBuildings;
                case BlockType.Special: return specialBuildings;
                default: return residentialBuildings;
            }
        }
        
        private void GenerateRoads()
        {
            if (roadPrefab == null) return;
            
            // Generate horizontal roads
            for (int x = -citySize / 2; x <= citySize / 2; x++)
            {
                for (int z = -citySize / 2; z < citySize / 2; z++)
                {
                    if (z % 2 == 0) // Every other row
                    {
                        Vector3 roadPosition = new Vector3(x * blockSize, 0, z * blockSize + blockSize / 2);
                        GameObject road = Instantiate(roadPrefab, roadPosition, Quaternion.identity);
                        road.name = $"Road_H_{x}_{z}";
                    }
                }
            }
            
            // Generate vertical roads
            for (int x = -citySize / 2; x < citySize / 2; x++)
            {
                for (int z = -citySize / 2; z <= citySize / 2; z++)
                {
                    if (x % 2 == 0) // Every other column
                    {
                        Vector3 roadPosition = new Vector3(x * blockSize + blockSize / 2, 0, z * blockSize);
                        GameObject road = Instantiate(roadPrefab, roadPosition, Quaternion.Euler(0, 90, 0));
                        road.name = $"Road_V_{x}_{z}";
                    }
                }
            }
        }
        
        private void AddEnvironmentalDetails()
        {
            // Add street lights
            if (streetLightPrefab != null)
            {
                for (int x = -citySize / 2; x < citySize / 2; x += 2)
                {
                    for (int z = -citySize / 2; z < citySize / 2; z += 2)
                    {
                        Vector3 lightPosition = new Vector3(x * blockSize, 0, z * blockSize);
                        GameObject streetLight = Instantiate(streetLightPrefab, lightPosition, Quaternion.identity);
                        streetLight.name = $"StreetLight_{x}_{z}";
                    }
                }
            }
            
            // Add trees and vegetation
            if (treePrefab != null)
            {
                for (int i = 0; i < citySize * 10; i++)
                {
                    Vector3 treePosition = new Vector3(
                        Random.Range(-citySize * blockSize / 2f, citySize * blockSize / 2f),
                        0,
                        Random.Range(-citySize * blockSize / 2f, citySize * blockSize / 2f)
                    );
                    
                    // Don't place trees on roads
                    if (IsPositionOnRoad(treePosition)) continue;
                    
                    GameObject tree = Instantiate(treePrefab, treePosition, Quaternion.identity);
                    tree.name = $"Tree_{i}";
                }
            }
        }
        
        private bool IsPositionOnRoad(Vector3 position)
        {
            // Simple check - in a full implementation, this would be more sophisticated
            float blockX = position.x / blockSize;
            float blockZ = position.z / blockSize;
            
            return (Mathf.FloorToInt(blockX) % 2 == 0) || (Mathf.FloorToInt(blockZ) % 2 == 0);
        }
        
        private void CreateTerritoryMarkers()
        {
            if (territoryMarkerPrefab == null) return;
            
            var cartelManager = GameManager.Instance?.cartelManager;
            if (cartelManager == null) return;
            
            var territories = cartelManager.GetTerritories();
            
            foreach (var territory in territories)
            {
                Vector3 markerPosition = new Vector3(
                    territory.Value.location.x,
                    5f, // Elevated above ground
                    territory.Value.location.z
                );
                
                GameObject marker = Instantiate(territoryMarkerPrefab, markerPosition, Quaternion.identity);
                marker.name = $"Territory_{territory.Key}_{territory.Value.name}";
                
                // Set material based on owner
                Renderer renderer = marker.GetComponent<Renderer>();
                if (renderer != null)
                {
                    switch (territory.Value.owner)
                    {
                        case "Player":
                            renderer.material = playerTerritoryMaterial;
                            break;
                        case "Neutral":
                            renderer.material = neutralTerritoryMaterial;
                            break;
                        default:
                            renderer.material = enemyTerritoryMaterial;
                            break;
                    }
                }
                
                territoryMarkers.Add(marker);
            }
        }
        
        public void UpdateTerritoryMarkers()
        {
            // Clear existing markers
            foreach (var marker in territoryMarkers)
            {
                if (marker != null) DestroyImmediate(marker);
            }
            territoryMarkers.Clear();
            
            // Recreate markers
            CreateTerritoryMarkers();
        }
        
        public CityBlock GetBlockAt(Vector2Int coordinate)
        {
            return cityBlocks.ContainsKey(coordinate) ? cityBlocks[coordinate] : null;
        }
        
        public List<CityBlock> GetBlocksInRadius(Vector3 center, float radius)
        {
            List<CityBlock> blocksInRadius = new List<CityBlock>();
            
            foreach (var block in cityBlocks.Values)
            {
                if (Vector3.Distance(block.position, center) <= radius)
                {
                    blocksInRadius.Add(block);
                }
            }
            
            return blocksInRadius;
        }
    }
    
    [System.Serializable]
    public class CityBlock
    {
        public Vector2Int coordinate;
        public Vector3 position;
        public BlockType blockType;
        public List<GameObject> buildings;
    }
    
    public enum BlockType
    {
        Residential,
        Commercial,
        Industrial,
        Special
    }
}
