using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Minimal Game Manager that works without any dependencies
    /// </summary>
    public class MinimalGameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public float gameSpeed = 1.0f;
        public bool isPaused = false;
        
        // Singleton pattern
        public static MinimalGameManager Instance { get; private set; }
        
        // Simple money tracking
        public float cleanMoney = 1000f;
        public float dirtyMoney = 0f;
        public float policeHeat = 0f;
        
        private void Awake()
        {
            // Singleton setup
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                Debug.Log("Street Empire - Minimal Game Manager Initialized");
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            Time.timeScale = gameSpeed;
            Debug.Log($"Starting with ${cleanMoney:F0} clean money");
        }
        
        private void Update()
        {
            HandleInput();
            UpdateSystems();
        }
        
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.Space))
            {
                TogglePause();
            }
            
            // Speed controls
            if (Input.GetKeyDown(KeyCode.Alpha1)) SetGameSpeed(0.5f);
            if (Input.GetKeyDown(KeyCode.Alpha2)) SetGameSpeed(1.0f);
            if (Input.GetKeyDown(KeyCode.Alpha3)) SetGameSpeed(2.0f);
            if (Input.GetKeyDown(KeyCode.Alpha4)) SetGameSpeed(3.0f);
            if (Input.GetKeyDown(KeyCode.Alpha5)) SetGameSpeed(5.0f);
        }
        
        private void UpdateSystems()
        {
            // Simple passive income
            if (!isPaused)
            {
                cleanMoney += 10f * Time.deltaTime; // $10 per second
                
                // Slowly reduce heat
                if (policeHeat > 0)
                {
                    policeHeat = Mathf.Max(0, policeHeat - 5f * Time.deltaTime);
                }
            }
        }
        
        public void TogglePause()
        {
            isPaused = !isPaused;
            Time.timeScale = isPaused ? 0f : gameSpeed;
            Debug.Log(isPaused ? "Game Paused" : "Game Resumed");
        }
        
        public void SetGameSpeed(float speed)
        {
            gameSpeed = Mathf.Clamp(speed, 0.1f, 5.0f);
            if (!isPaused)
            {
                Time.timeScale = gameSpeed;
            }
            Debug.Log($"Game Speed: {gameSpeed}x");
        }
        
        // Simple drug dealing simulation
        public void SellDrugs(float amount)
        {
            dirtyMoney += amount;
            policeHeat += amount * 0.1f; // Increase heat
            Debug.Log($"Sold drugs for ${amount:F0}. Heat: {policeHeat:F1}, Dirty Money: ${dirtyMoney:F0}");
        }
        
        public void LaunderMoney(float amount)
        {
            if (dirtyMoney >= amount)
            {
                dirtyMoney -= amount;
                cleanMoney += amount * 0.85f; // 15% fee
                Debug.Log($"Laundered ${amount:F0} (Fee: 15%). Clean: ${cleanMoney:F0}, Dirty: ${dirtyMoney:F0}");
            }
            else
            {
                Debug.Log("Not enough dirty money to launder");
            }
        }
        
        public void BribePolice(float amount)
        {
            if (cleanMoney >= amount)
            {
                cleanMoney -= amount;
                policeHeat = Mathf.Max(0, policeHeat - amount * 0.5f);
                Debug.Log($"Bribed police for ${amount:F0}. Heat reduced to {policeHeat:F1}");
            }
            else
            {
                Debug.Log("Not enough clean money for bribe");
            }
        }
        
        // Display current status
        private void OnGUI()
        {
            // Simple on-screen display
            GUI.Box(new Rect(10, 10, 300, 120), "Street Empire - Drug Dealing Sim");
            
            GUI.Label(new Rect(20, 35, 280, 20), $"Clean Money: ${cleanMoney:F0}");
            GUI.Label(new Rect(20, 55, 280, 20), $"Dirty Money: ${dirtyMoney:F0}");
            GUI.Label(new Rect(20, 75, 280, 20), $"Police Heat: {policeHeat:F1}");
            GUI.Label(new Rect(20, 95, 280, 20), $"Game Speed: {gameSpeed:F1}x");
            
            // Action buttons
            if (GUI.Button(new Rect(10, 140, 100, 30), "Sell Drugs"))
            {
                SellDrugs(Random.Range(50f, 200f));
            }
            
            if (GUI.Button(new Rect(120, 140, 100, 30), "Launder $500"))
            {
                LaunderMoney(500f);
            }
            
            if (GUI.Button(new Rect(230, 140, 100, 30), "Bribe $200"))
            {
                BribePolice(200f);
            }
            
            // Instructions
            GUI.Box(new Rect(10, 180, 320, 100), "");
            GUI.Label(new Rect(20, 190, 300, 80), 
                "CONTROLS:\n" +
                "WASD - Move\n" +
                "Space - Pause\n" +
                "1-5 - Game Speed\n" +
                "E - Sell Drugs");
        }
    }
}
