{"name": "com.unity.modules.unitywebrequestwww", "version": "1.0.0", "type": "module", "displayName": "Unity Web Request WWW", "description": "The UnityWebRequestWWW module implements the legacy WWW lets you communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html", "icon": null, "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}