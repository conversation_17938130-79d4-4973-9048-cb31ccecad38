{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 1164, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 1164, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 1164, "tid": 13, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 1164, "tid": 13, "ts": 1752408700881360, "dur": 10, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881381, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 1164, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 1164, "tid": 1, "ts": 1752408700714910, "dur": 939, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1164, "tid": 1, "ts": 1752408700715850, "dur": 2792, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 1164, "tid": 1, "ts": 1752408700718643, "dur": 800, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881386, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 1164, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700714874, "dur": 9952, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700724827, "dur": 153984, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700724835, "dur": 19, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700724857, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700725080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700725082, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700725121, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700725128, "dur": 1012, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726144, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726204, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726207, "dur": 44, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726255, "dur": 2, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726258, "dur": 35, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726295, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726297, "dur": 32, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726332, "dur": 29, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726362, "dur": 25, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726389, "dur": 28, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726421, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726423, "dur": 39, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726464, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726466, "dur": 38, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726508, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726541, "dur": 24, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726568, "dur": 27, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726597, "dur": 17, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726617, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726643, "dur": 16, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726661, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726677, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726695, "dur": 13, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726710, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726727, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726744, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726762, "dur": 7, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726771, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726772, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726800, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726826, "dur": 14, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726841, "dur": 14, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726858, "dur": 14, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726874, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726894, "dur": 17, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726913, "dur": 26, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726940, "dur": 13, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726956, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726979, "dur": 17, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700726998, "dur": 15, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727015, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727033, "dur": 14, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727049, "dur": 13, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727064, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727079, "dur": 59, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727140, "dur": 1, "ph": "X", "name": "ProcessMessages 1544", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727141, "dur": 20, "ph": "X", "name": "ReadAsync 1544", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727164, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727188, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727207, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727224, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727242, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727258, "dur": 15, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727275, "dur": 15, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727292, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727313, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727333, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727354, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727380, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727402, "dur": 29, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727433, "dur": 13, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727448, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727467, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727485, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727499, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727518, "dur": 15, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727535, "dur": 14, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727551, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727569, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727586, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727603, "dur": 12, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727617, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727618, "dur": 13, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727633, "dur": 14, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727649, "dur": 14, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727665, "dur": 17, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727684, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727702, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727721, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727736, "dur": 15, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727753, "dur": 13, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727768, "dur": 12, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727784, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727808, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727822, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727836, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727858, "dur": 124, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727986, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700727989, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728018, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728019, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728100, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728174, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728176, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728222, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728225, "dur": 94, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728322, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728325, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728376, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728380, "dur": 34, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728415, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728417, "dur": 24, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728444, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728446, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728482, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728485, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728520, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728522, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728552, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728578, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728579, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728632, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728664, "dur": 47, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728713, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728716, "dur": 41, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728759, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728760, "dur": 175, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728940, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700728968, "dur": 656, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700729627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700729629, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700729664, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700729669, "dur": 142930, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700872608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700872612, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700872638, "dur": 886, "ph": "X", "name": "ProcessMessages 2890", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700873526, "dur": 1886, "ph": "X", "name": "ReadAsync 2890", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700875414, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700875417, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 1164, "tid": 25769803776, "ts": 1752408700875454, "dur": 3352, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881394, "dur": 291, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 1164, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 1164, "tid": 21474836480, "ts": 1752408700714839, "dur": 4631, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 1164, "tid": 21474836480, "ts": 1752408700719471, "dur": 5293, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 1164, "tid": 21474836480, "ts": 1752408700724766, "dur": 30, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881687, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 1164, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 1164, "tid": 17179869184, "ts": 1752408700709324, "dur": 169527, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 1164, "tid": 17179869184, "ts": 1752408700709414, "dur": 5389, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 1164, "tid": 17179869184, "ts": 1752408700878854, "dur": 81, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 1164, "tid": 17179869184, "ts": 1752408700878876, "dur": 20, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881693, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752408700724905, "dur": 850, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700725762, "dur": 71, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700725857, "dur": 366, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700726258, "dur": 1726, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700727985, "dur": 146432, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700874422, "dur": 132, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700874554, "dur": 222, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700875554, "dur": 1129, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752408700726324, "dur": 1697, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408700728064, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408700728153, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408700728357, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752408700728356, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752408700728647, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408700728778, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408700729107, "dur": 145330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700726276, "dur": 1728, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700728007, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752408700728072, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700728150, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700728344, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752408700728426, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700728694, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700728821, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408700729016, "dur": 145418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700726266, "dur": 1730, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700728005, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752408700728123, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700728282, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700728536, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752408700728682, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700728815, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408700729096, "dur": 145348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408700726235, "dur": 1755, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408700727992, "dur": 802, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408700728794, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408700729111, "dur": 145311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700726314, "dur": 1698, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728017, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752408700728069, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728144, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728288, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752408700728287, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752408700728465, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728612, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728793, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408700728854, "dur": 145559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700726357, "dur": 1671, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700728039, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752408700728030, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752408700728189, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700728455, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700728617, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700728790, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408700729108, "dur": 145317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408700726397, "dur": 1638, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408700728141, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728140, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728296, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728295, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728512, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728674, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728672, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408700728770, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408700728854, "dur": 145554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408700726436, "dur": 1607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408700728047, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752408700728291, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752408700728290, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752408700728656, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408700728781, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408700729072, "dur": 145371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408700726468, "dur": 1583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408700728055, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752408700728260, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408700728441, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408700728672, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408700728848, "dur": 145561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700726520, "dur": 1539, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700728061, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752408700728288, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700728638, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700728766, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700728832, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408700728909, "dur": 145523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408700726541, "dur": 1524, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408700728068, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728280, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728278, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728344, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408700728485, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728666, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728665, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408700728784, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408700728852, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408700729069, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752408700729868, "dur": 142888, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752408700726575, "dur": 1497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408700728076, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752408700728278, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408700728653, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752408700728652, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752408700728779, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408700728839, "dur": 145577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408700726603, "dur": 1476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408700728081, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752408700728518, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752408700728659, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408700728786, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408700729077, "dur": 145353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408700726638, "dur": 1446, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408700728086, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752408700728273, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408700728617, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408700728775, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408700729294, "dur": 145152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408700726667, "dur": 1423, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408700728092, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752408700728244, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408700728446, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408700728602, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752408700728678, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408700728873, "dur": 145566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700726702, "dur": 1394, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700728097, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752408700728279, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700728491, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752408700728580, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700728653, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700728828, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408700728900, "dur": 145541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408700877925, "dur": 658, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 1164, "tid": 13, "ts": 1752408700881771, "dur": 231, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 1164, "tid": 13, "ts": 1752408700882048, "dur": 7364, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 1164, "tid": 13, "ts": 1752408700881373, "dur": 8077, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}