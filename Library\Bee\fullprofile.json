{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 29380, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 29380, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 29380, "tid": 8, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 29380, "tid": 8, "ts": 1752408231668070, "dur": 898, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231674417, "dur": 874, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 29380, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 29380, "tid": 1, "ts": 1752408230860715, "dur": 4143, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 29380, "tid": 1, "ts": 1752408230864862, "dur": 16274, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 29380, "tid": 1, "ts": 1752408230881149, "dur": 5828, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231675295, "dur": 779, "ph": "X", "name": "", "args": {}}, {"pid": 29380, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230854571, "dur": 839, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230855411, "dur": 803127, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230856210, "dur": 3310, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230859527, "dur": 1498, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230861029, "dur": 220, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230861253, "dur": 527, "ph": "X", "name": "ProcessMessages 13276", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408230861782, "dur": 773271, "ph": "X", "name": "ReadAsync 13276", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231635062, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231635065, "dur": 674, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231635742, "dur": 22315, "ph": "X", "name": "ProcessMessages 2890", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231658062, "dur": 187, "ph": "X", "name": "ReadAsync 2890", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231658251, "dur": 191, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 29380, "tid": 12884901888, "ts": 1752408231658444, "dur": 12, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231676075, "dur": 15, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 29380, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 29380, "tid": 8589934592, "ts": 1752408230851748, "dur": 35280, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 29380, "tid": 8589934592, "ts": 1752408230887030, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 29380, "tid": 8589934592, "ts": 1752408230887032, "dur": 1209, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231676093, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 29380, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 29380, "tid": 4294967296, "ts": 1752408230823612, "dur": 835735, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 29380, "tid": 4294967296, "ts": 1752408230829459, "dur": 15964, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 29380, "tid": 4294967296, "ts": 1752408231659357, "dur": 6285, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 29380, "tid": 4294967296, "ts": 1752408231661866, "dur": 2551, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 29380, "tid": 4294967296, "ts": 1752408231665697, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231676100, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752408230852751, "dur": 999, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408230853761, "dur": 70, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408230853859, "dur": 520, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408230855184, "dur": 1710, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752408230854404, "dur": 3045, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408230857451, "dur": 778207, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231635662, "dur": 130, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231635794, "dur": 172, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231636040, "dur": 67, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231636182, "dur": 52, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231639881, "dur": 1228, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752408230854366, "dur": 3095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408230857476, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752408230857553, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408230857663, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408230857963, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408230858087, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752408230858172, "dur": 777489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230854408, "dur": 3077, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230857545, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1752408230857489, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752408230857611, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230857678, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752408230857793, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230857982, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230858101, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752408230858413, "dur": 777269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230854397, "dur": 3079, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230857537, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1752408230857480, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752408230857601, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230857693, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1752408230857691, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752408230857748, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230857978, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230858099, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752408230858386, "dur": 777295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408230854450, "dur": 3050, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408230857503, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752408230857558, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408230857680, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752408230857975, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408230858091, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752408230858564, "dur": 777102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230854438, "dur": 3055, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230857497, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752408230857585, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230857673, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230857984, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752408230857983, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752408230858096, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230858160, "dur": 29412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752408230887572, "dur": 748092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408230854485, "dur": 3021, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408230857509, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752408230857568, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408230857749, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408230858001, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1752408230857987, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752408230858113, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752408230858409, "dur": 777284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230854513, "dur": 3001, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230857560, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230857878, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230858004, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752408230858003, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408230858097, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230858177, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752408230858436, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752408230858756, "dur": 50, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752408230859401, "dur": 774622, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752408230854542, "dur": 2983, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408230857560, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408230857679, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408230857968, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408230858141, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752408230858225, "dur": 777450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408230854571, "dur": 2958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408230857563, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408230857723, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752408230857722, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752408230857865, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752408230857997, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752408230857995, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752408230858138, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752408230858196, "dur": 777459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408230854614, "dur": 2922, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408230857569, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408230857658, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408230857976, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752408230858168, "dur": 777504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408230854640, "dur": 2902, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408230857567, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408230857928, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752408230857927, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752408230858118, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752408230858425, "dur": 777263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230854667, "dur": 2882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230857589, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230857766, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230857992, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230858104, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752408230858419, "dur": 777266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230854691, "dur": 2868, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230857611, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230857723, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230857995, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230858131, "dur": 133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752408230858264, "dur": 777392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230854717, "dur": 2850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230857596, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230857897, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752408230857993, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230858155, "dur": 29021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230887177, "dur": 390, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752408230887568, "dur": 748106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230854745, "dur": 2828, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230857601, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230857686, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752408230857685, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752408230857756, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230857940, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752408230858015, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230858146, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752408230858226, "dur": 777451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408230854778, "dur": 2802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408230857613, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408230857672, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752408230857835, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752408230857984, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408230858096, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752408230858379, "dur": 777299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752408231642077, "dur": 536, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 29380, "tid": 8, "ts": 1752408231676511, "dur": 2850, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 29380, "tid": 8, "ts": 1752408231679424, "dur": 1815, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 29380, "tid": 8, "ts": 1752408231672813, "dur": 9291, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}