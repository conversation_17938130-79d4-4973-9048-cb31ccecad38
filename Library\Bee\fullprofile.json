{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 10796, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 10796, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 10796, "tid": 8, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 10796, "tid": 8, "ts": 1752409049385262, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385286, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 10796, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 10796, "tid": 1, "ts": 1752409049228882, "dur": 990, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10796, "tid": 1, "ts": 1752409049229874, "dur": 2924, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 10796, "tid": 1, "ts": 1752409049232799, "dur": 952, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385293, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 10796, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049228847, "dur": 14446, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243295, "dur": 141451, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243309, "dur": 34, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243344, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243346, "dur": 282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243637, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243715, "dur": 11, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049243727, "dur": 982, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244713, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244760, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244763, "dur": 32, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244797, "dur": 29, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244830, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244831, "dur": 33, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244867, "dur": 26, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244897, "dur": 27, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244926, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244956, "dur": 28, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049244986, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245015, "dur": 28, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245047, "dur": 62, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245113, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245164, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245166, "dur": 40, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245212, "dur": 45, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245260, "dur": 43, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245305, "dur": 36, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245344, "dur": 32, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245378, "dur": 29, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245410, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245412, "dur": 35, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245449, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245450, "dur": 24, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245478, "dur": 28, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245508, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245528, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245553, "dur": 15, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245570, "dur": 12, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245585, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245601, "dur": 13, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245616, "dur": 29, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245647, "dur": 15, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245664, "dur": 31, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245697, "dur": 15, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245713, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245735, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245751, "dur": 13, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245767, "dur": 13, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245781, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245801, "dur": 14, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245818, "dur": 16, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245836, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245856, "dur": 26, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245884, "dur": 13, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245899, "dur": 16, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245917, "dur": 14, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245932, "dur": 16, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245950, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245975, "dur": 14, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049245991, "dur": 14, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246007, "dur": 12, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246021, "dur": 21, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246044, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246070, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246072, "dur": 14, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246088, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246114, "dur": 13, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246130, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246148, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246164, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246181, "dur": 26, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246210, "dur": 13, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246225, "dur": 24, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246250, "dur": 10, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246262, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246282, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246299, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246318, "dur": 16, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246335, "dur": 12, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246350, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246371, "dur": 15, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246390, "dur": 18, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246410, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246434, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246448, "dur": 20, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246471, "dur": 13, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246486, "dur": 17, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246505, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246521, "dur": 147, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246672, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246731, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246734, "dur": 38, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246777, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246779, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246818, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246820, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246861, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246862, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246902, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246904, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246943, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049246945, "dur": 138, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247087, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247089, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247135, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247138, "dur": 69, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247211, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247212, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247249, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247251, "dur": 137, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247392, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049247420, "dur": 616, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049248039, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049248062, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049248065, "dur": 130339, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049378413, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049378417, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049378443, "dur": 460, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049378905, "dur": 2201, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049381109, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049381137, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 10796, "tid": 34359738368, "ts": 1752409049381141, "dur": 3600, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385304, "dur": 342, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 10796, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 10796, "tid": 30064771072, "ts": 1752409049228734, "dur": 5055, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 10796, "tid": 30064771072, "ts": 1752409049233790, "dur": 9511, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 10796, "tid": 30064771072, "ts": 1752409049243302, "dur": 42, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385649, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 10796, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 10796, "tid": 25769803776, "ts": 1752409049226533, "dur": 158247, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 10796, "tid": 25769803776, "ts": 1752409049226688, "dur": 1997, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 10796, "tid": 25769803776, "ts": 1752409049384783, "dur": 65, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 10796, "tid": 25769803776, "ts": 1752409049384796, "dur": 20, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 10796, "tid": 25769803776, "ts": 1752409049384850, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385662, "dur": 19, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752409049243606, "dur": 872, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049244487, "dur": 67, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049244583, "dur": 401, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049245001, "dur": 1829, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049246831, "dur": 133548, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049380380, "dur": 280, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049380660, "dur": 57, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752409049381421, "dur": 1104, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752409049245000, "dur": 1838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752409049246841, "dur": 633, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752409049247475, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752409049247826, "dur": 132580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752409049245073, "dur": 1788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752409049246865, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752409049247019, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752409049247156, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752409049247378, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752409049247476, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752409049247743, "dur": 132697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049245110, "dur": 1767, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049246919, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049246996, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049247164, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049247396, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752409049247536, "dur": 132919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049245443, "dur": 1512, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049246989, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049247097, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049247187, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049247391, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752409049247481, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752409049247534, "dur": 132936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752409049245065, "dur": 1787, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752409049246857, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752409049246910, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752409049247007, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752409049247190, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752409049247248, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752409049247382, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752409049247380, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752409049247535, "dur": 132927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049245104, "dur": 1764, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049246996, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049247145, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049247392, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049247500, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752409049247618, "dur": 132827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049245136, "dur": 1749, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049246930, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049247017, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049247117, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049247374, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049247472, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752409049247769, "dur": 132630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752409049245169, "dur": 1723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752409049247000, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752409049247107, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752409049247163, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752409049247405, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752409049247524, "dur": 132960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752409049245201, "dur": 1697, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752409049247001, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752409049247000, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752409049247097, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752409049247244, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752409049247381, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752409049247490, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752409049247748, "dur": 132677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049245241, "dur": 1664, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247025, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247117, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247235, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247388, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247497, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752409049247768, "dur": 132649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049245277, "dur": 1635, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247025, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247100, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247285, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247367, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247467, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752409049247763, "dur": 132628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752409049245306, "dur": 1615, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752409049246953, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752409049247025, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752409049247024, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752409049247236, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752409049247359, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752409049247463, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752409049247939, "dur": 132445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752409049245343, "dur": 1586, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752409049246970, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752409049247031, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752409049247029, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752409049247384, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752409049247383, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752409049247463, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752409049247527, "dur": 132845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752409049245381, "dur": 1557, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752409049247036, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752409049247035, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752409049247146, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752409049247145, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752409049247224, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752409049247394, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752409049247552, "dur": 132879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752409049245411, "dur": 1536, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752409049247044, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752409049247043, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752409049247106, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752409049247422, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752409049247529, "dur": 132847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752409049245033, "dur": 1811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752409049246851, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752409049246908, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752409049247037, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752409049247129, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752409049247387, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752409049247386, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752409049247463, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752409049247544, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752409049247695, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752409049248459, "dur": 130261, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752409049383690, "dur": 897, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 10796, "tid": 8, "ts": 1752409049385762, "dur": 1777, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 10796, "tid": 8, "ts": 1752409049387653, "dur": 8707, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 10796, "tid": 8, "ts": 1752409049385275, "dur": 11150, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}