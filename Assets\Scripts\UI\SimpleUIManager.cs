using UnityEngine;
using UnityEngine.UI;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Simple UI Manager that works without TextMeshPro dependencies
    /// </summary>
    public class SimpleUIManager : MonoBehaviour
    {
        [Header("UI Elements")]
        public Text moneyText;
        public Text heatText;
        public Text gameSpeedText;
        public Text instructionsText;
        
        [Header("Buttons")]
        public Button sellDrugsButton;
        public Button launderMoneyButton;
        public Button bribePoliceButton;
        
        private SimpleGameManager gameManager;
        
        private void Start()
        {
            gameManager = SimpleGameManager.Instance;
            
            if (gameManager != null)
            {
                // Subscribe to events
                gameManager.OnMoneyChanged += UpdateMoneyDisplay;
                gameManager.OnHeatChanged += UpdateHeatDisplay;
                gameManager.OnGameSpeedChanged += UpdateSpeedDisplay;
            }
            
            // Set up buttons
            if (sellDrugsButton != null)
                sellDrugsButton.onClick.AddListener(() => gameManager?.SellDrugs(100f));
            
            if (launderMoneyButton != null)
                launderMoneyButton.onClick.AddListener(() => gameManager?.LaunderMoney(500f));
            
            if (bribePoliceButton != null)
                bribePoliceButton.onClick.AddListener(() => gameManager?.BribePolice(200f));
            
            // Set up instructions
            if (instructionsText != null)
            {
                instructionsText.text = "CONTROLS:\n" +
                    "WASD - Move\n" +
                    "Shift - Run\n" +
                    "Space - Pause\n" +
                    "1-5 - Game Speed\n" +
                    "E - Interact\n\n" +
                    "Click buttons to test systems!";
            }
            
            UpdateAllDisplays();
        }
        
        private void UpdateMoneyDisplay(float clean, float dirty)
        {
            if (moneyText != null)
            {
                moneyText.text = $"Clean: ${clean:F0}\nDirty: ${dirty:F0}";
            }
        }
        
        private void UpdateHeatDisplay(float heat)
        {
            if (heatText != null)
            {
                heatText.text = $"Police Heat: {heat:F1}";
                
                // Change color based on heat level
                if (heat < 30f)
                    heatText.color = Color.green;
                else if (heat < 70f)
                    heatText.color = Color.yellow;
                else
                    heatText.color = Color.red;
            }
        }
        
        private void UpdateSpeedDisplay(float speed)
        {
            if (gameSpeedText != null)
            {
                gameSpeedText.text = $"Speed: {speed:F1}x";
            }
        }
        
        private void UpdateAllDisplays()
        {
            if (gameManager != null)
            {
                UpdateMoneyDisplay(gameManager.cleanMoney, gameManager.dirtyMoney);
                UpdateHeatDisplay(gameManager.policeHeat);
                UpdateSpeedDisplay(gameManager.gameSpeed);
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (gameManager != null)
            {
                gameManager.OnMoneyChanged -= UpdateMoneyDisplay;
                gameManager.OnHeatChanged -= UpdateHeatDisplay;
                gameManager.OnGameSpeedChanged -= UpdateSpeedDisplay;
            }
        }
    }
}
