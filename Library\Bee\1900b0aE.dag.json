{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "Outputs": [], "ToBuildDependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98], "OverwriteOutputs": false, "DebugActionIndex": 0}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.UnityAdditionalFile.txt", "DisplayName": "Writing StreetEmpire.UnityAdditionalFile.txt", "ActionType": "WriteFile", "PayloadOffset": 97, "PayloadLength": 28, "PayloadDebugContentSnippet": "C:\\Users\\<USER>\\Desktop\\GAMEE", "Inputs": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.UnityAdditionalFile.txt"], "DebugActionIndex": 1}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp", "DisplayName": "Writing StreetEmpire.rsp", "ActionType": "WriteFile", "PayloadOffset": 208, "PayloadLength": 28325, "PayloadDebugContentSnippet": "-target:library\n-out:\"Library/", "Inputs": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp"], "DebugActionIndex": 2}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp2", "DisplayName": "Writing StreetEmpire.rsp2", "ActionType": "WriteFile", "PayloadOffset": 28617, "PayloadLength": 0, "PayloadDebugContentSnippet": "", "Inputs": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp2"], "DebugActionIndex": 3}, {"Annotation": "Csc Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll (+2 others)", "DisplayName": "Csc StreetEmpire", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\NetCoreRuntime\\dotnet.exe\" exec \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp\" \"@Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp2\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEditor.Graphs.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll", "Assets/Scripts/Cartel/CartelData.cs", "Assets/Scripts/Cartel/CartelManager.cs", "Assets/Scripts/Core/GameManager.cs", "Assets/Scripts/Core/MinimalGameManager.cs", "Assets/Scripts/Core/SimpleGameManager.cs", "Assets/Scripts/Drugs/DrugData.cs", "Assets/Scripts/Drugs/DrugManager.cs", "Assets/Scripts/Economy/EconomyData.cs", "Assets/Scripts/Economy/MoneyManager.cs", "Assets/Scripts/Environment/BuildingInteraction.cs", "Assets/Scripts/Environment/CityGenerator.cs", "Assets/Scripts/Features/BulkSalesManager.cs", "Assets/Scripts/Features/FishingManager.cs", "Assets/Scripts/Player/CameraController.cs", "Assets/Scripts/Player/MinimalPlayerController.cs", "Assets/Scripts/Player/PlayerController.cs", "Assets/Scripts/Player/SimplePlayerController.cs", "Assets/Scripts/Police/PoliceData.cs", "Assets/Scripts/Police/PoliceManager.cs", "Assets/Scripts/Property/PropertyData.cs", "Assets/Scripts/Property/PropertyManager.cs", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll", "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp"], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll", "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.pdb", "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.ref.dll"], "ToBuildDependencies": [1, 2, 3, 94], "ToUseDependencies": [1, 3], "FrontendResponseFiles": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.rsp"], "AllowUnexpectedOutput": true, "Env": [{"Key": "DOTNET_MULTILEVEL_LOOKUP", "Value": "0"}], "DebugActionIndex": 4}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.Graphs.dll_DD64C5227A7571DB", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEditor.Graphs.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 5}, {"Annotation": "ScriptAssemblies", "DisplayName": null, "Inputs": [], "Outputs": [], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 95, 96], "OverwriteOutputs": false, "DebugActionIndex": 6}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.CoreModule.dll_D9A6C509560AD0A9", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 7}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 8}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 9}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.dll_12D88E704AB8989C", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.dll_12D88E704AB8989C.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 10}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 11}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 12}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 13}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 14}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 15}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3F734F5833FEADB.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.SceneViewModule.dll_D3F734F5833FEADB", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.SceneViewModule.dll_D3F734F5833FEADB.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3F734F5833FEADB.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 16}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 17}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 18}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 19}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_067413C2B719E945.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.UIElementsModule.dll_067413C2B719E945", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsModule.dll_067413C2B719E945.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_067413C2B719E945.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 20}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 21}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 22}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_648FDA6173531F0D.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AccessibilityModule.dll_648FDA6173531F0D", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AccessibilityModule.dll_648FDA6173531F0D.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_648FDA6173531F0D.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 23}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 24}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_84BCFC960039581B.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AndroidJNIModule.dll_84BCFC960039581B", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AndroidJNIModule.dll_84BCFC960039581B.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_84BCFC960039581B.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 25}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AnimationModule.dll_63CCCE166879F273", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 26}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_CD1FE17E093B02A6.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ARModule.dll_CD1FE17E093B02A6", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ARModule.dll_CD1FE17E093B02A6.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_CD1FE17E093B02A6.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 27}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 28}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_27F5F86C90D0FEA5.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.AudioModule.dll_27F5F86C90D0FEA5", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.AudioModule.dll_27F5F86C90D0FEA5.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_27F5F86C90D0FEA5.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 29}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8EDE55676AEDDD89.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ClothModule.dll_8EDE55676AEDDD89", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClothModule.dll_8EDE55676AEDDD89.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8EDE55676AEDDD89.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 30}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 31}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 32}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 33}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_FCC7985BFE103BD4.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.CoreModule.dll_FCC7985BFE103BD4", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CoreModule.dll_FCC7985BFE103BD4.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_FCC7985BFE103BD4.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 34}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_28C9303179F47BEC.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.CrashReportingModule.dll_28C9303179F47BEC", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.CrashReportingModule.dll_28C9303179F47BEC.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_28C9303179F47BEC.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 35}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EF8575F8F180DEF1.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.DirectorModule.dll_EF8575F8F180DEF1", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DirectorModule.dll_EF8575F8F180DEF1.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EF8575F8F180DEF1.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 36}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_457CD548A92C2581.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.dll_457CD548A92C2581", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.dll_457CD548A92C2581.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.dll_457CD548A92C2581.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 37}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 38}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 39}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_ABE9658847CC8BF7.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.GIModule.dll_ABE9658847CC8BF7", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GIModule.dll_ABE9658847CC8BF7.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_ABE9658847CC8BF7.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 40}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.GridModule.dll_DA38B44A1664D1CB", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 41}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 42}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 43}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_605597D357029286.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.IMGUIModule.dll_605597D357029286", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.IMGUIModule.dll_605597D357029286.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_605597D357029286.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 44}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 45}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.InputModule.dll_8E6BC01D07092349", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 46}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 47}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1988DFD28A3A999E.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.LocalizationModule.dll_1988DFD28A3A999E", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.LocalizationModule.dll_1988DFD28A3A999E.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1988DFD28A3A999E.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 48}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 49}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 50}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.Physics2DModule.dll_B072BE070976EA08", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 51}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_864B31AF48902016.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.PhysicsModule.dll_864B31AF48902016", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PhysicsModule.dll_864B31AF48902016.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_864B31AF48902016.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 52}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 53}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DC4585EF3965AD48.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.PropertiesModule.dll_DC4585EF3965AD48", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.PropertiesModule.dll_DC4585EF3965AD48.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DC4585EF3965AD48.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 54}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 55}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 56}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 57}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 58}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 59}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1861BB66B801A9B1.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.StreamingModule.dll_1861BB66B801A9B1", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.StreamingModule.dll_1861BB66B801A9B1.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1861BB66B801A9B1.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 60}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 61}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02E41FA2B631C699.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.SubsystemsModule.dll_02E41FA2B631C699", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.SubsystemsModule.dll_02E41FA2B631C699.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02E41FA2B631C699.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 62}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A091AD1E1E4E6904.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TerrainModule.dll_A091AD1E1E4E6904", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainModule.dll_A091AD1E1E4E6904.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A091AD1E1E4E6904.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 63}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TerrainPhysicsModule.dll_7C56225101490207", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 64}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 65}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 66}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 67}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TilemapModule.dll_3F8E149F360B4A0D", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 68}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 69}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UIElementsModule.dll_0722810AB95E0290", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 70}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UIModule.dll_0F20BAA80864A989", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 71}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 72}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 73}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 74}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityConnectModule.dll_48FF24793BBED48A", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 75}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_96E96F47D88DE460.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityCurlModule.dll_96E96F47D88DE460", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityCurlModule.dll_96E96F47D88DE460.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_96E96F47D88DE460.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 76}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 77}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 78}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 79}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 80}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 81}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 82}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.VehiclesModule.dll_67486F786BAAAE8C", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 83}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.VFXModule.dll_79AD232C9E28E4D7", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 84}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.VideoModule.dll_4CFF7CD753631A4A", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 85}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 86}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.VRModule.dll_61F2E12DAA1F2F01", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 87}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.WindModule.dll_731673848F46BA3D", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 88}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm", "DisplayName": "MovedFromExtractor UnityEngine.XRModule.dll_94B1A43233597C5F", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 89}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\PlaybackEngines\\MetroSupport\\UnityEditor.UWP.Extensions.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 90}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 91}, {"Annotation": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm", "DisplayName": "MovedFromExtractor UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library\\Bee\\artifacts\\mvdfrm\\UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm\" \"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe"], "Outputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm"], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 92}, {"Annotation": "WriteText Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm.rsp", "DisplayName": "Writing StreetEmpire.dll.mvfrm.rsp", "ActionType": "WriteFile", "PayloadOffset": 28710, "PayloadLength": 7525, "PayloadDebugContentSnippet": "Library\\Bee\\artifacts\\mvdfrm\\U", "Inputs": [], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm.rsp"], "DebugActionIndex": 93}, {"Annotation": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm", "DisplayName": "MovedFromExtractorCombine StreetEmpire.dll", "Action": "\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe\" \"Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm\" \"@Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm.rsp\"", "Inputs": ["Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_DD64C5227A7571DB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_D9A6C509560AD0A9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_75A1CF56C9779C68.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_BF648D2075AE74B6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.dll_12D88E704AB8989C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0E4AFE0C73F04D38.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_954751ED8E1D1DCD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_50AD8ACF907575D2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4A740E2DCE85710F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_31EA6ECA72DCF770.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3F734F5833FEADB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_B85CEED5CE93F007.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_004525475E17A925.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E9CFFF0A00D59701.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_067413C2B719E945.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B634AB856C48D378.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_5B9FA06A4136D3DF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_648FDA6173531F0D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_13AD1C7AAC1DA3DD.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_84BCFC960039581B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_63CCCE166879F273.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_CD1FE17E093B02A6.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_8E5D411F30EC2601.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_27F5F86C90D0FEA5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8EDE55676AEDDD89.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_DB3C157FF2BEA6A5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_7B4E91C83932A9EC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_27739A5FFF6ECDEE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_FCC7985BFE103BD4.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_28C9303179F47BEC.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EF8575F8F180DEF1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.dll_457CD548A92C2581.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F4D6D76263E1B012.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_AF0A46B0C67A490C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_ABE9658847CC8BF7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_DA38B44A1664D1CB.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E5158C1C43AD0DF5.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_E7408F78BB5761E0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_605597D357029286.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1FA25C3DC8DDDBF2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8E6BC01D07092349.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C4A7639D728E7F97.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_1988DFD28A3A999E.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_7BDB797DE806077A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_F179E5940BB0A493.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B072BE070976EA08.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_864B31AF48902016.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_21CBAF2F8ADA65FE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_DC4585EF3965AD48.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_6E50F9CDB68C6AD1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_DE8F112286FAABF9.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_6B51CE4E32855D30.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_9836B940E7E9DE86.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A7BE6E79E68E5E0B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_1861BB66B801A9B1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F50CCFB00354A7BF.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_02E41FA2B631C699.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_A091AD1E1E4E6904.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_7C56225101490207.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_36BC6A28E191AED2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_7EA4A09A711567F0.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_07CE832D3E322BEE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3F8E149F360B4A0D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_F8CE8ABDC7BB7B6A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0722810AB95E0290.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_0F20BAA80864A989.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_A2CAA4851DF7AA18.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_8CB8A2D8259942F1.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_9B7AEA47942D5976.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_48FF24793BBED48A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_96E96F47D88DE460.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_71F76177C64428CE.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_91D7180D0A48A4C7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_69763972921B6F2B.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_F8DF9722C64BFC5F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_26C41DF2DCB96860.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_4966E5F660D5245C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_67486F786BAAAE8C.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_79AD232C9E28E4D7.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_4CFF7CD753631A4A.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_AB46D008E6AFC572.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_61F2E12DAA1F2F01.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_731673848F46BA3D.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_94B1A43233597C5F.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_496B13DAB1CFAAD2.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_F14844FE9E847F17.mvfrm", "Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BEF49B495D8F8C87.mvfrm", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/ScriptUpdater/ApiUpdater.MovedFromExtractor.exe", "Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm.rsp"], "Outputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll.mvfrm"], "ToBuildDependencies": [5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 94}, {"Annotation": "CopyFiles Library/ScriptAssemblies/StreetEmpire.dll", "DisplayName": "Copying StreetEmpire.dll", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.dll"], "Outputs": ["Library/ScriptAssemblies/StreetEmpire.dll"], "ToBuildDependencies": [4], "DebugActionIndex": 95}, {"Annotation": "CopyFiles Library/ScriptAssemblies/StreetEmpire.pdb", "DisplayName": "Copying StreetEmpire.pdb", "ActionType": "CopyFiles", "Inputs": ["Library/Bee/artifacts/1900b0aE.dag/StreetEmpire.pdb"], "Outputs": ["Library/ScriptAssemblies/StreetEmpire.pdb"], "ToBuildDependencies": [4], "DebugActionIndex": 96}, {"Annotation": "BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json", "DisplayName": "BuildPlayerDataGenerator TypeDb-All", "Action": "\"D:/UnityHubEditor/2022.3.45f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe\" \"D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe\" -a=\"C:\\Users\\<USER>\\Desktop\\GAMEE\\Library\\ScriptAssemblies\\StreetEmpire.dll\" -s=\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\Managed\\UnityEngine\" -s=\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\" -s=\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\" -s=\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\" -s=\"D:\\UnityHubEditor\\2022.3.45f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\" -s=\"C:\\Users\\<USER>\\Desktop\\GAMEE\\Library\\ScriptAssemblies\" -o=\"Library/BuildPlayerData/Editor\" -rn=\"\" -tn=\"TypeDb-All.json\"", "Inputs": ["D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/netcorerun/netcorerun.exe", "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe", "Library/ScriptAssemblies/StreetEmpire.dll"], "Outputs": ["Library/BuildPlayerData/Editor/TypeDb-All.json"], "ToBuildDependencies": [95], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 97}, {"Annotation": "ScriptAssembliesAndTypeDB", "DisplayName": null, "Inputs": [], "Outputs": [], "ToBuildDependencies": [6, 97], "OverwriteOutputs": false, "DebugActionIndex": 98}], "FileSignatures": [{"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Stevedore.Program.exe"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/BeeLocalCacheTool.exe"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/NiceIO.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "Library/Bee/1900b0aE-inputdata.json"}], "StatSignatures": [{"File": "Assets/csc.rsp"}, {"File": "Assets/mcs.rsp"}, {"File": "Assets/Scripts/csc.rsp"}, {"File": "Library/Bee/1900b0aE-inputdata.json"}], "GlobSignatures": [{"Path": "D:/UnityHubEditor/2022.3.45f1/Editor/Data/Tools/BuildPipeline"}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".pdb"], "StructuredLogFileName": "Library/Bee/tundra.log.json", "StateFileName": "Library/Bee/TundraBuildState.state", "StateFileNameTmp": "Library/Bee/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/Bee/TundraBuildState.state.map", "ScanCacheFileName": "Library/Bee/tundra.scancache", "ScanCacheFileNameTmp": "Library/Bee/tundra.scancache.tmp", "DigestCacheFileName": "Library/Bee/tundra.digestcache", "DigestCacheFileNameTmp": "Library/Bee/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/Bee/CachedNodeOutput", "EmitDataForBeeWhy": 0, "NamedNodes": {"all_tundra_nodes": 0, "ScriptAssemblies": 6, "ScriptAssembliesAndTypeDB": 98}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/Bee/1900b0aE.dag.json", "PayloadsFile": "C:/Users/<USER>/Desktop/GAMEE/Library/Bee/1900b0aE.dag.payloads", "RelativePathToRoot": "../.."}