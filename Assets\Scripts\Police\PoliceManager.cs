using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages police heat, investigations, raids, and corruption mechanics
    /// </summary>
    public class PoliceManager : MonoBehaviour
    {
        [Header("Heat Settings")]
        public float maxHeat = 100f;
        public float heatDecayRate = 1f; // Heat lost per hour
        public float baseRaidThreshold = 70f;
        
        [Header("Investigation Settings")]
        public float investigationDuration = 72f; // Hours
        public float evidenceThreshold = 50f;
        
        [Header("Corruption Settings")]
        public float maxCorruption = 100f;
        public float corruptionDecayRate = 0.5f;
        public float bribeEffectiveness = 0.8f;
        
        // Current state
        private float currentHeat = 0f;
        private float currentCorruption = 0f;
        private List<Investigation> activeInvestigations = new List<Investigation>();
        private List<CorruptOfficer> corruptOfficers = new List<CorruptOfficer>();
        
        // Events
        public System.Action<float> OnHeatChanged;
        public System.Action<float> OnCorruptionChanged;
        public System.Action<Investigation> OnInvestigationStarted;
        public System.Action<Investigation> OnInvestigationCompleted;
        public System.Action<Raid> OnRaidStarted;
        public System.Action<CorruptOfficer> OnOfficerCorrupted;
        
        private void Start()
        {
            InitializePoliceSystem();
        }
        
        private void Update()
        {
            UpdateHeat();
            UpdateCorruption();
            UpdateInvestigations();
            CheckForRaids();
        }
        
        private void InitializePoliceSystem()
        {
            // Start with some base corruption
            currentCorruption = 10f;
            OnHeatChanged?.Invoke(currentHeat);
            OnCorruptionChanged?.Invoke(currentCorruption);
        }
        
        private void UpdateHeat()
        {
            // Natural heat decay over time
            if (currentHeat > 0)
            {
                float decay = heatDecayRate * Time.deltaTime / 3600f; // Per hour
                currentHeat = Mathf.Max(0, currentHeat - decay);
                OnHeatChanged?.Invoke(currentHeat);
            }
        }
        
        private void UpdateCorruption()
        {
            // Corruption slowly decays without maintenance
            if (currentCorruption > 0)
            {
                float decay = corruptionDecayRate * Time.deltaTime / 3600f; // Per hour
                currentCorruption = Mathf.Max(0, currentCorruption - decay);
                OnCorruptionChanged?.Invoke(currentCorruption);
            }
        }
        
        private void UpdateInvestigations()
        {
            for (int i = activeInvestigations.Count - 1; i >= 0; i--)
            {
                var investigation = activeInvestigations[i];
                investigation.remainingTime -= Time.deltaTime;
                
                // Accumulate evidence over time
                float evidenceRate = GetEvidenceAccumulationRate(investigation);
                investigation.evidenceLevel += evidenceRate * Time.deltaTime;
                
                if (investigation.remainingTime <= 0)
                {
                    CompleteInvestigation(investigation);
                    activeInvestigations.RemoveAt(i);
                }
            }
        }
        
        public void AddHeat(float amount, string reason = "Criminal Activity")
        {
            // Corruption reduces heat gain
            float corruptionReduction = (currentCorruption / maxCorruption) * 0.5f; // Up to 50% reduction
            float actualHeat = amount * (1f - corruptionReduction);
            
            currentHeat = Mathf.Min(maxHeat, currentHeat + actualHeat);
            OnHeatChanged?.Invoke(currentHeat);
            
            Debug.Log($"Heat increased by {actualHeat:F1} ({reason}). Current heat: {currentHeat:F1}");
            
            // High heat might trigger investigations
            if (currentHeat > 50f && Random.value < 0.1f)
            {
                StartInvestigation();
            }
        }
        
        public bool TryBribe(float amount)
        {
            var moneyManager = GameManager.Instance.moneyManager;
            if (!moneyManager.SpendCleanMoney(amount, "Police Bribe"))
            {
                return false;
            }
            
            // Bribe effectiveness depends on amount and current corruption
            float effectiveness = Mathf.Min(1f, (amount / 10000f) * bribeEffectiveness);
            float heatReduction = currentHeat * effectiveness * 0.3f; // Up to 30% heat reduction
            float corruptionIncrease = effectiveness * 20f;
            
            currentHeat = Mathf.Max(0, currentHeat - heatReduction);
            currentCorruption = Mathf.Min(maxCorruption, currentCorruption + corruptionIncrease);
            
            OnHeatChanged?.Invoke(currentHeat);
            OnCorruptionChanged?.Invoke(currentCorruption);
            
            Debug.Log($"Bribe successful! Heat reduced by {heatReduction:F1}, corruption increased by {corruptionIncrease:F1}");
            return true;
        }
        
        public bool CorruptOfficer(string officerName, float bribeAmount)
        {
            var moneyManager = GameManager.Instance.moneyManager;
            if (!moneyManager.SpendCleanMoney(bribeAmount, $"Corrupt Officer {officerName}"))
            {
                return false;
            }
            
            var officer = new CorruptOfficer
            {
                name = officerName,
                corruptionLevel = Random.Range(0.3f, 0.8f),
                monthlyPayment = bribeAmount * 0.1f, // 10% monthly maintenance
                lastPayment = Time.time,
                isActive = true
            };
            
            corruptOfficers.Add(officer);
            OnOfficerCorrupted?.Invoke(officer);
            
            Debug.Log($"Officer {officerName} corrupted for ${bribeAmount:F0}");
            return true;
        }
        
        private void StartInvestigation()
        {
            var investigation = new Investigation
            {
                id = System.Guid.NewGuid().ToString(),
                type = (InvestigationType)Random.Range(0, System.Enum.GetValues(typeof(InvestigationType)).Length),
                totalTime = investigationDuration * 3600f, // Convert to seconds
                remainingTime = investigationDuration * 3600f,
                evidenceLevel = 0f,
                startTime = Time.time
            };
            
            activeInvestigations.Add(investigation);
            OnInvestigationStarted?.Invoke(investigation);
            
            Debug.Log($"Investigation started: {investigation.type}");
        }
        
        private void CompleteInvestigation(Investigation investigation)
        {
            investigation.isCompleted = true;
            
            if (investigation.evidenceLevel >= evidenceThreshold)
            {
                // Investigation successful - trigger raid
                TriggerRaid(investigation);
            }
            else
            {
                // Investigation failed - reduce heat
                currentHeat = Mathf.Max(0, currentHeat - 20f);
                OnHeatChanged?.Invoke(currentHeat);
            }
            
            OnInvestigationCompleted?.Invoke(investigation);
        }
        
        private void TriggerRaid(Investigation investigation)
        {
            var raid = new Raid
            {
                id = System.Guid.NewGuid().ToString(),
                type = GetRaidTypeFromInvestigation(investigation.type),
                severity = Mathf.Clamp01(investigation.evidenceLevel / 100f),
                warningTime = GetWarningTime(),
                startTime = Time.time + GetWarningTime()
            };
            
            OnRaidStarted?.Invoke(raid);
            Debug.Log($"Raid incoming! Type: {raid.type}, Severity: {raid.severity:F2}, Warning: {raid.warningTime:F1}s");
        }
        
        private float GetWarningTime()
        {
            // Corrupt officers provide better warnings
            float baseWarning = Random.Range(30f, 120f); // 30 seconds to 2 minutes
            float corruptionBonus = (currentCorruption / maxCorruption) * 300f; // Up to 5 minutes bonus
            
            return baseWarning + corruptionBonus;
        }
        
        private RaidType GetRaidTypeFromInvestigation(InvestigationType investigationType)
        {
            switch (investigationType)
            {
                case InvestigationType.DrugProduction: return RaidType.LabRaid;
                case InvestigationType.MoneyLaundering: return RaidType.BusinessRaid;
                case InvestigationType.Distribution: return RaidType.StreetSweep;
                default: return RaidType.GeneralRaid;
            }
        }
        
        private float GetEvidenceAccumulationRate(Investigation investigation)
        {
            float baseRate = 1f; // Evidence per hour
            
            // Corruption slows evidence accumulation
            float corruptionEffect = 1f - ((currentCorruption / maxCorruption) * 0.7f); // Up to 70% slower
            
            return baseRate * corruptionEffect / 3600f; // Convert to per second
        }
        
        private void CheckForRaids()
        {
            // Random raids based on heat level
            if (currentHeat > baseRaidThreshold)
            {
                float raidChance = (currentHeat - baseRaidThreshold) / (maxHeat - baseRaidThreshold);
                if (Random.value < raidChance * Time.deltaTime * 0.0001f) // Very low chance per frame
                {
                    TriggerRandomRaid();
                }
            }
        }
        
        private void TriggerRandomRaid()
        {
            var raid = new Raid
            {
                id = System.Guid.NewGuid().ToString(),
                type = (RaidType)Random.Range(0, System.Enum.GetValues(typeof(RaidType)).Length),
                severity = Random.Range(0.3f, 0.8f),
                warningTime = GetWarningTime(),
                startTime = Time.time + GetWarningTime()
            };
            
            OnRaidStarted?.Invoke(raid);
        }
        
        // Getters
        public float GetHeat() => currentHeat;
        public float GetCorruption() => currentCorruption;
        public List<Investigation> GetActiveInvestigations() => new List<Investigation>(activeInvestigations);
        public List<CorruptOfficer> GetCorruptOfficers() => new List<CorruptOfficer>(corruptOfficers);
    }
}
