using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Manages rival cartels, territory control, and cartel warfare
    /// </summary>
    public class CartelManager : MonoBehaviour
    {
        [Header("Cartel Settings")]
        public int maxActiveCartels = 3;
        public float territoryConflictChance = 0.1f;
        public float cartelGrowthRate = 1.0f;
        
        [Header("Territory Settings")]
        public int totalTerritories = 20;
        public float territoryIncomeMultiplier = 100f;
        
        // Active cartels
        private List<Cartel> activeCartels = new List<Cartel>();
        private Dictionary<int, TerritoryData> territories = new Dictionary<int, TerritoryData>();
        
        // Player cartel data
        private PlayerCartel playerCartel;
        
        // Events
        public System.Action<Cartel> OnCartelSpawned;
        public System.Action<Cartel> OnCartelDestroyed;
        public System.Action<CartelConflict> OnConflictStarted;
        public System.Action<CartelConflict> OnConflictResolved;
        public System.Action<int, string> OnTerritoryChanged; // territoryId, newOwner
        public System.Action<CartelMeeting> OnMeetingAvailable;
        
        private void Start()
        {
            InitializeCartelSystem();
        }
        
        private void Update()
        {
            UpdateCartels();
            CheckForConflicts();
            UpdateTerritories();
        }
        
        private void InitializeCartelSystem()
        {
            // Initialize player cartel
            playerCartel = new PlayerCartel
            {
                name = "Street Empire",
                reputation = 10f,
                territory = new List<int> { 0 }, // Start with one territory
                members = new List<CartelMember>(),
                resources = 1000f
            };
            
            // Initialize territories
            for (int i = 0; i < totalTerritories; i++)
            {
                territories[i] = new TerritoryData
                {
                    id = i,
                    name = GenerateTerritoryName(i),
                    owner = i == 0 ? "Player" : "Neutral",
                    value = Random.Range(50f, 200f),
                    securityLevel = Random.Range(0.1f, 0.9f),
                    drugDemand = GenerateDrugDemand()
                };
            }
            
            // Spawn initial rival cartels
            SpawnInitialCartels();
        }
        
        private void SpawnInitialCartels()
        {
            string[] cartelNames = { "Los Hermanos", "Blood Diamonds", "Iron Wolves", "Shadow Syndicate", "Crimson Tide" };
            
            for (int i = 0; i < Mathf.Min(maxActiveCartels, cartelNames.Length); i++)
            {
                SpawnCartel(cartelNames[i]);
            }
        }
        
        private void SpawnCartel(string name)
        {
            var cartel = new Cartel
            {
                id = System.Guid.NewGuid().ToString(),
                name = name,
                strength = Random.Range(20f, 80f),
                aggression = Random.Range(0.2f, 0.8f),
                territory = new List<int>(),
                resources = Random.Range(5000f, 20000f),
                specialization = (CartelSpecialization)Random.Range(0, System.Enum.GetValues(typeof(CartelSpecialization)).Length)
            };
            
            // Assign initial territory
            var availableTerritories = territories.Where(t => t.Value.owner == "Neutral").ToList();
            if (availableTerritories.Count > 0)
            {
                var territory = availableTerritories[Random.Range(0, availableTerritories.Count)];
                cartel.territory.Add(territory.Key);
                territories[territory.Key].owner = cartel.name;
                OnTerritoryChanged?.Invoke(territory.Key, cartel.name);
            }
            
            activeCartels.Add(cartel);
            OnCartelSpawned?.Invoke(cartel);
            
            Debug.Log($"Cartel spawned: {cartel.name} (Strength: {cartel.strength:F1})");
        }
        
        private void UpdateCartels()
        {
            foreach (var cartel in activeCartels)
            {
                // Cartels grow stronger over time
                cartel.strength += cartelGrowthRate * Time.deltaTime;
                
                // Generate resources from territory
                float territoryIncome = cartel.territory.Count * territoryIncomeMultiplier * Time.deltaTime;
                cartel.resources += territoryIncome;
                
                // AI decision making
                MakeCartelDecisions(cartel);
            }
        }
        
        private void MakeCartelDecisions(Cartel cartel)
        {
            // Simple AI: try to expand territory if strong enough
            if (cartel.strength > 50f && Random.value < 0.001f) // Low chance per frame
            {
                AttemptTerritoryExpansion(cartel);
            }
            
            // Consider attacking player if they're too strong
            if (playerCartel.reputation > cartel.strength * 1.5f && Random.value < 0.0005f)
            {
                InitiateConflictWithPlayer(cartel);
            }
        }
        
        private void AttemptTerritoryExpansion(Cartel cartel)
        {
            // Find adjacent neutral or weak territories
            var targetTerritories = territories.Where(t => 
                t.Value.owner == "Neutral" || 
                (t.Value.owner != cartel.name && t.Value.owner != "Player")).ToList();
            
            if (targetTerritories.Count > 0)
            {
                var target = targetTerritories[Random.Range(0, targetTerritories.Count)];
                AttemptTakeover(cartel, target.Key);
            }
        }
        
        private void AttemptTakeover(Cartel attacker, int territoryId)
        {
            var territory = territories[territoryId];
            
            if (territory.owner == "Player")
            {
                // Attack player territory
                InitiatePlayerTerritoryAttack(attacker, territoryId);
            }
            else if (territory.owner == "Neutral")
            {
                // Easy takeover
                territory.owner = attacker.name;
                attacker.territory.Add(territoryId);
                OnTerritoryChanged?.Invoke(territoryId, attacker.name);
            }
            else
            {
                // Attack another cartel
                var defender = activeCartels.FirstOrDefault(c => c.name == territory.owner);
                if (defender != null)
                {
                    InitiateCartelConflict(attacker, defender, territoryId);
                }
            }
        }
        
        private void InitiatePlayerTerritoryAttack(Cartel attacker, int territoryId)
        {
            var conflict = new CartelConflict
            {
                id = System.Guid.NewGuid().ToString(),
                attackingCartel = attacker.name,
                defendingCartel = "Player",
                territoryId = territoryId,
                startTime = Time.time,
                isPlayerInvolved = true,
                conflictType = ConflictType.TerritoryAttack
            };
            
            OnConflictStarted?.Invoke(conflict);
            Debug.Log($"{attacker.name} is attacking your territory: {territories[territoryId].name}!");
        }
        
        private void InitiateCartelConflict(Cartel attacker, Cartel defender, int territoryId)
        {
            var conflict = new CartelConflict
            {
                id = System.Guid.NewGuid().ToString(),
                attackingCartel = attacker.name,
                defendingCartel = defender.name,
                territoryId = territoryId,
                startTime = Time.time,
                isPlayerInvolved = false,
                conflictType = ConflictType.CartelWar
            };
            
            // Resolve automatically for NPC vs NPC
            ResolveCartelConflict(conflict, attacker, defender);
        }
        
        private void ResolveCartelConflict(CartelConflict conflict, Cartel attacker, Cartel defender)
        {
            float attackerPower = attacker.strength + Random.Range(-10f, 10f);
            float defenderPower = defender.strength + Random.Range(-10f, 10f);
            
            if (attackerPower > defenderPower)
            {
                // Attacker wins
                territories[conflict.territoryId].owner = attacker.name;
                attacker.territory.Add(conflict.territoryId);
                defender.territory.Remove(conflict.territoryId);
                
                // Damage to loser
                defender.strength *= 0.8f;
                
                OnTerritoryChanged?.Invoke(conflict.territoryId, attacker.name);
            }
            else
            {
                // Defender wins
                attacker.strength *= 0.9f;
            }
            
            OnConflictResolved?.Invoke(conflict);
        }
        
        private void InitiateConflictWithPlayer(Cartel cartel)
        {
            var conflict = new CartelConflict
            {
                id = System.Guid.NewGuid().ToString(),
                attackingCartel = cartel.name,
                defendingCartel = "Player",
                territoryId = -1, // General conflict
                startTime = Time.time,
                isPlayerInvolved = true,
                conflictType = ConflictType.Ambush
            };
            
            OnConflictStarted?.Invoke(conflict);
            Debug.Log($"{cartel.name} has initiated a conflict with you!");
        }
        
        public bool AttackCartelTerritory(string cartelName, int territoryId)
        {
            var targetCartel = activeCartels.FirstOrDefault(c => c.name == cartelName);
            if (targetCartel == null) return false;
            
            var conflict = new CartelConflict
            {
                id = System.Guid.NewGuid().ToString(),
                attackingCartel = "Player",
                defendingCartel = cartelName,
                territoryId = territoryId,
                startTime = Time.time,
                isPlayerInvolved = true,
                conflictType = ConflictType.TerritoryAttack
            };
            
            OnConflictStarted?.Invoke(conflict);
            return true;
        }
        
        public void GenerateCartelMeeting()
        {
            if (activeCartels.Count == 0) return;
            
            var cartel = activeCartels[Random.Range(0, activeCartels.Count)];
            var meeting = new CartelMeeting
            {
                cartelName = cartel.name,
                meetingType = (MeetingType)Random.Range(0, System.Enum.GetValues(typeof(MeetingType)).Length),
                location = GenerateMeetingLocation(),
                scheduledTime = Time.time + Random.Range(3600f, 7200f), // 1-2 hours
                reward = Random.Range(5000f, 20000f),
                risk = Random.Range(0.1f, 0.8f)
            };
            
            OnMeetingAvailable?.Invoke(meeting);
        }
        
        private void CheckForConflicts()
        {
            if (Random.value < territoryConflictChance * Time.deltaTime)
            {
                // Random conflict between cartels
                if (activeCartels.Count >= 2)
                {
                    var cartel1 = activeCartels[Random.Range(0, activeCartels.Count)];
                    var cartel2 = activeCartels[Random.Range(0, activeCartels.Count)];
                    
                    if (cartel1 != cartel2 && cartel1.territory.Count > 0)
                    {
                        var territoryId = cartel1.territory[Random.Range(0, cartel1.territory.Count)];
                        InitiateCartelConflict(cartel2, cartel1, territoryId);
                    }
                }
            }
        }
        
        private void UpdateTerritories()
        {
            // Update territory values and demands over time
            foreach (var territory in territories.Values)
            {
                // Slight random fluctuation in value
                territory.value += Random.Range(-1f, 1f) * Time.deltaTime;
                territory.value = Mathf.Max(10f, territory.value);
            }
        }
        
        private string GenerateTerritoryName(int id)
        {
            string[] areas = { "Downtown", "Industrial", "Suburbs", "Docks", "University", "Airport", "Mall", "Park" };
            string[] suffixes = { "District", "Zone", "Area", "Sector", "Quarter", "Heights", "Gardens", "Plaza" };
            
            return $"{areas[id % areas.Length]} {suffixes[Random.Range(0, suffixes.Length)]}";
        }
        
        private Dictionary<DrugType, float> GenerateDrugDemand()
        {
            var demand = new Dictionary<DrugType, float>();
            foreach (DrugType drugType in System.Enum.GetValues(typeof(DrugType)))
            {
                demand[drugType] = Random.Range(0.1f, 2.0f);
            }
            return demand;
        }
        
        private Vector3 GenerateMeetingLocation()
        {
            return new Vector3(Random.Range(-100f, 100f), 0f, Random.Range(-100f, 100f));
        }
        
        // Getters
        public List<Cartel> GetActiveCartels() => new List<Cartel>(activeCartels);
        public Dictionary<int, TerritoryData> GetTerritories() => new Dictionary<int, TerritoryData>(territories);
        public PlayerCartel GetPlayerCartel() => playerCartel;
    }
}
