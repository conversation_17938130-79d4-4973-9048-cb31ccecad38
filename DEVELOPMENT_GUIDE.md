# Street Empire - Development Guide

## Project Overview
Street Empire is an original drug dealing simulation game inspired by Schedule 1 and Drug Dealer Simulator, built with Unity 2022.3 LTS. The game features enhanced mechanics, better visuals, and 12+ unique features based on community feedback.

## Core Systems Implemented

### 1. Game Management System
- **GameManager.cs**: Central game state controller
- Singleton pattern for global access
- Game speed control and pause functionality
- Save/load system foundation

### 2. Drug Production System
- **DrugManager.cs**: Handles all drug operations
- **DrugData.cs**: Data structures for drugs, batches, and recipes
- Advanced chemistry with quality and purity systems
- Production queues with time-based completion
- 8 different drug types: Cannabis, Cocaine, Meth, MDMA, Heroin, Mushrooms, LSD, Fentanyl

### 3. Economy System
- **MoneyManager.cs**: Clean vs dirty money management
- **EconomyData.cs**: Transaction history and business data
- Money laundering through legitimate businesses
- Dynamic market pricing with supply/demand
- 8 business types for laundering operations

### 4. Police System
- **PoliceManager.cs**: Heat, investigations, and raids
- **PoliceData.cs**: Investigation and raid data structures
- Corruption mechanics with bribable officers
- Evidence system with break-in opportunities
- Parkour escape sequences

### 5. Cartel Warfare System
- **CartelManager.cs**: Rival cartels and territory control
- **CartelData.cs**: Cartel AI and conflict resolution
- 20 territories with dynamic ownership
- Cartel meetings and alliance opportunities
- AI personalities and behavior patterns

### 6. Property Management
- **PropertyManager.cs**: Property ownership and upgrades
- **PropertyData.cs**: Property types and customization
- 4 property types: Safe Houses, Labs, Warehouses, Businesses
- Upgrade system with security and capacity bonuses
- Property market with dynamic pricing

### 7. Enhanced Features

#### Bulk Sales System
- **BulkSalesManager.cs**: Special customer groups
- 6 customer types: Biker Gangs, Business People, College, Tourists, Local Dealers, Night Club Owners
- Time-limited events with special requirements
- Loyalty system affecting repeat business

#### Fishing Mini-Game
- **FishingManager.cs**: Relaxing side activity
- 3 fishing spots with different rewards
- Hidden contraband and valuable items
- Fish sales for clean money generation

### 8. UI System
- **UIManager.cs**: Comprehensive interface management
- Real-time HUD with money, heat, and corruption displays
- Notification system for events
- Panel management for different game sections

## 12+ Enhanced Features

1. **Cartel Warfare System**: Dynamic rival cartels with territory battles
2. **Police Investigation Mechanics**: Heat system with raids and corruption
3. **Empire Management**: Hire employees with skills and loyalty
4. **Property Investment**: Buy and customize multiple property types
5. **Bulk Sales Events**: Special customers like biker gangs and business groups
6. **Advanced Chemistry**: Realistic drug mixing with quality systems
7. **Parkour Escape System**: Chase sequences with climbing mechanics
8. **Underground Economy**: Black market trading and smuggling
9. **Reputation System**: Build street cred and unlock opportunities
10. **Fishing Mini-game**: Relaxing activity with hidden rewards
11. **Money Laundering**: Complex business-based cleaning operations
12. **Dynamic Market**: Supply/demand pricing with economic events

## Technical Architecture

### Unity Version
- Unity 2022.3 LTS for stability and long-term support
- Enhanced rendering pipeline for better visuals
- Post-processing effects for cinematic look

### Code Structure
- Namespace: `StreetEmpire.Core`
- Modular design with separate managers
- Event-driven architecture for loose coupling
- ScriptableObject databases for data management

### Performance Considerations
- Object pooling for UI elements
- Efficient update loops with time-based checks
- Minimal garbage collection through proper data structures

## Getting Started

### 1. Unity Setup
1. Install Unity 2022.3 LTS
2. Create new 3D project
3. Import all scripts from Assets/Scripts/
4. Set up scene with GameManager prefab

### 2. Scene Configuration
1. Create GameManager GameObject
2. Attach all manager scripts
3. Configure UI Canvas with panels
4. Set up camera and lighting

### 3. Database Setup
1. Create DrugDatabase ScriptableObject
2. Configure drug types and recipes
3. Set up property and business data
4. Initialize cartel and customer databases

## Development Roadmap

### Phase 1: Core Implementation ✅
- [x] Basic game systems
- [x] Drug production mechanics
- [x] Money management
- [x] Police heat system
- [x] Property system

### Phase 2: Enhanced Features (Current)
- [x] Cartel warfare
- [x] Bulk sales system
- [x] Fishing mini-game
- [ ] Parkour mechanics
- [ ] Advanced AI systems

### Phase 3: Visual Polish
- [ ] 3D models and animations
- [ ] Particle effects
- [ ] Post-processing pipeline
- [ ] Audio implementation
- [ ] UI/UX improvements

### Phase 4: Content & Balance
- [ ] Additional drug types
- [ ] More customer types
- [ ] Seasonal events
- [ ] Achievement system
- [ ] Multiplayer foundation

## Key Differences from Schedule 1

### Original Features
- **Fishing Mini-game**: Unique relaxation activity with contraband rewards
- **Cartel Warfare**: Dynamic territory control with AI opponents
- **Bulk Sales Events**: Time-limited opportunities with special customers
- **Advanced Chemistry**: Quality and purity affect pricing
- **Property Customization**: Detailed upgrade and decoration systems

### Improved Mechanics
- **Better Police AI**: More realistic investigation and corruption
- **Enhanced Economy**: Separate clean/dirty money with laundering
- **Territory System**: 20 territories with strategic importance
- **Employee Management**: Hire specialists with unique abilities
- **Dynamic Events**: Weather, holidays, and market fluctuations

## Legal Considerations
- Fictional content for mature audiences only
- No promotion of illegal activities
- Educational/entertainment purposes
- Age rating: M for Mature (17+)

## Community Features
- Steam Workshop integration planned
- Mod support through ScriptableObjects
- Community feedback integration
- Regular content updates

This development guide provides the foundation for creating a unique and engaging drug dealing simulation that improves upon existing games while maintaining originality.
