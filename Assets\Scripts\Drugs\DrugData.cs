using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Data structures for drug system
    /// </summary>
    
    [System.Serializable]
    public class DrugBatch
    {
        public DrugType drugType;
        public int quantity;
        public float quality; // 0-1 range
        public float purity; // 0-1 range
        public System.DateTime productionDate;
        public List<Ingredient> ingredients;
        
        public float GetMarketValue()
        {
            var basePrice = DrugDatabase.GetBasePrice(drugType);
            var qualityMultiplier = 0.5f + (quality * 1.5f); // 0.5x to 2x based on quality
            var purityMultiplier = 0.8f + (purity * 0.4f); // 0.8x to 1.2x based on purity
            
            return basePrice * qualityMultiplier * purityMultiplier;
        }
    }
    
    [System.Serializable]
    public class Ingredient
    {
        public string name;
        public IngredientType type;
        public float qualityBonus;
        public float purityBonus;
        public float cost;
        public bool isIllegal;
        
        public Ingredient(string name, IngredientType type, float qualityBonus = 0f, float purityBonus = 0f, float cost = 1f, bool isIllegal = false)
        {
            this.name = name;
            this.type = type;
            this.qualityBonus = qualityBonus;
            this.purityBonus = purityBonus;
            this.cost = cost;
            this.isIllegal = isIllegal;
        }
    }
    
    [System.Serializable]
    public class ProductionOrder
    {
        public DrugType drugType;
        public int quantity;
        public float totalTime;
        public float remainingTime;
        public List<Ingredient> ingredients;
        public float startTime;
        
        public float Progress => 1f - (remainingTime / totalTime);
    }
    
    [System.Serializable]
    public class DrugTypeData
    {
        public DrugType drugType;
        public string displayName;
        public string description;
        public float basePrice;
        public float complexityMultiplier;
        public List<Ingredient> requiredIngredients;
        public float riskLevel; // How much heat this generates
        public Sprite icon;
        
        public DrugTypeData(DrugType type, string name, string desc, float price, float complexity, float risk)
        {
            drugType = type;
            displayName = name;
            description = desc;
            basePrice = price;
            complexityMultiplier = complexity;
            riskLevel = risk;
            requiredIngredients = new List<Ingredient>();
        }
    }
    
    public enum DrugType
    {
        Cannabis,
        Cocaine,
        Methamphetamine,
        MDMA,
        Heroin,
        Mushrooms,
        LSD,
        Fentanyl
    }
    
    public enum IngredientType
    {
        Plant,
        Chemical,
        Equipment,
        Additive,
        Cutting
    }
    
    /// <summary>
    /// Database containing all drug information and recipes
    /// </summary>
    [CreateAssetMenu(fileName = "DrugDatabase", menuName = "Street Empire/Drug Database")]
    public class DrugDatabase : ScriptableObject
    {
        [Header("Drug Types")]
        public List<DrugTypeData> drugTypes = new List<DrugTypeData>();
        
        [Header("Common Ingredients")]
        public List<Ingredient> commonIngredients = new List<Ingredient>();
        
        private void OnEnable()
        {
            InitializeDefaultData();
        }
        
        private void InitializeDefaultData()
        {
            if (drugTypes.Count == 0)
            {
                // Initialize with default drug data
                drugTypes.Add(new DrugTypeData(DrugType.Cannabis, "Cannabis", "High-quality marijuana", 10f, 1.0f, 0.2f));
                drugTypes.Add(new DrugTypeData(DrugType.Cocaine, "Cocaine", "Pure white powder", 100f, 2.0f, 0.8f));
                drugTypes.Add(new DrugTypeData(DrugType.Methamphetamine, "Methamphetamine", "Crystal blue persuasion", 80f, 2.5f, 0.9f));
                drugTypes.Add(new DrugTypeData(DrugType.MDMA, "MDMA", "Party enhancement pills", 50f, 1.8f, 0.6f));
                drugTypes.Add(new DrugTypeData(DrugType.Heroin, "Heroin", "Brown sugar", 150f, 3.0f, 1.0f));
                drugTypes.Add(new DrugTypeData(DrugType.Mushrooms, "Psilocybin Mushrooms", "Magic mushrooms", 30f, 1.2f, 0.4f));
            }
            
            if (commonIngredients.Count == 0)
            {
                // Initialize common ingredients
                commonIngredients.Add(new Ingredient("Baking Soda", IngredientType.Chemical, 0f, -0.1f, 1f, false));
                commonIngredients.Add(new Ingredient("Acetone", IngredientType.Chemical, 0.1f, 0.1f, 5f, true));
                commonIngredients.Add(new Ingredient("Pseudoephedrine", IngredientType.Chemical, 0.2f, 0.2f, 20f, true));
                commonIngredients.Add(new Ingredient("Fertilizer", IngredientType.Chemical, 0.1f, 0f, 3f, false));
            }
        }
        
        public DrugTypeData GetDrugData(DrugType drugType)
        {
            return drugTypes.Find(d => d.drugType == drugType);
        }
        
        public static float GetBasePrice(DrugType drugType)
        {
            // Static method for quick price lookup
            switch (drugType)
            {
                case DrugType.Cannabis: return 10f;
                case DrugType.Cocaine: return 100f;
                case DrugType.Methamphetamine: return 80f;
                case DrugType.MDMA: return 50f;
                case DrugType.Heroin: return 150f;
                case DrugType.Mushrooms: return 30f;
                default: return 1f;
            }
        }
    }
}
