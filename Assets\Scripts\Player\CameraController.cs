using UnityEngine;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Camera controller for following the player and providing cinematic views
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        [Header("Target Settings")]
        public Transform target;
        public float distance = 25f;
        public float height = 15f;
        
        [Header("Movement Settings")]
        public float rotationSpeed = 2f;
        public float followSpeed = 5f;
        public float zoomSpeed = 2f;
        public float minDistance = 5f;
        public float maxDistance = 50f;
        
        [Header("Input Settings")]
        public bool enableMouseControl = true;
        public bool enableKeyboardControl = true;
        public KeyCode freeLookKey = KeyCode.LeftAlt;
        
        private float currentRotationX = 0f;
        private float currentRotationY = 0f;
        private bool isFreeLook = false;
        private Vector3 lastTargetPosition;
        
        private void Start()
        {
            // If no target is set, try to find the player
            if (target == null)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    target = player.transform;
                }
            }
            
            if (target != null)
            {
                lastTargetPosition = target.position;
                
                // Initialize rotation based on current camera position
                Vector3 direction = (transform.position - target.position).normalized;
                currentRotationY = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg;
                currentRotationX = Mathf.Asin(direction.y) * Mathf.Rad2Deg;
            }
        }
        
        private void LateUpdate()
        {
            if (target == null) return;
            
            HandleInput();
            UpdateCameraPosition();
        }
        
        private void HandleInput()
        {
            // Check for free look mode
            isFreeLook = Input.GetKey(freeLookKey);
            
            // Mouse input for camera rotation
            if (enableMouseControl && (isFreeLook || Input.GetMouseButton(1)))
            {
                float mouseX = Input.GetAxis("Mouse X") * rotationSpeed;
                float mouseY = Input.GetAxis("Mouse Y") * rotationSpeed;
                
                currentRotationY += mouseX;
                currentRotationX -= mouseY;
                currentRotationX = Mathf.Clamp(currentRotationX, -80f, 80f);
            }
            
            // Keyboard input for camera rotation
            if (enableKeyboardControl)
            {
                if (Input.GetKey(KeyCode.Q))
                {
                    currentRotationY -= rotationSpeed * 50f * Time.deltaTime;
                }
                if (Input.GetKey(KeyCode.E))
                {
                    currentRotationY += rotationSpeed * 50f * Time.deltaTime;
                }
            }
            
            // Zoom input
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                distance -= scroll * zoomSpeed;
                distance = Mathf.Clamp(distance, minDistance, maxDistance);
            }
            
            // Keyboard zoom
            if (Input.GetKey(KeyCode.Plus) || Input.GetKey(KeyCode.KeypadPlus))
            {
                distance -= zoomSpeed * Time.deltaTime;
                distance = Mathf.Clamp(distance, minDistance, maxDistance);
            }
            if (Input.GetKey(KeyCode.Minus) || Input.GetKey(KeyCode.KeypadMinus))
            {
                distance += zoomSpeed * Time.deltaTime;
                distance = Mathf.Clamp(distance, minDistance, maxDistance);
            }
        }
        
        private void UpdateCameraPosition()
        {
            // Calculate desired position based on rotation and distance
            Quaternion rotation = Quaternion.Euler(currentRotationX, currentRotationY, 0);
            Vector3 direction = rotation * Vector3.back;
            
            Vector3 targetPosition = target.position;
            
            // Smooth follow if target is moving
            if (!isFreeLook)
            {
                targetPosition = Vector3.Lerp(lastTargetPosition, target.position, followSpeed * Time.deltaTime);
                lastTargetPosition = targetPosition;
            }
            
            Vector3 desiredPosition = targetPosition + direction * distance + Vector3.up * height;
            
            // Smooth camera movement
            transform.position = Vector3.Lerp(transform.position, desiredPosition, followSpeed * Time.deltaTime);
            
            // Always look at target
            Vector3 lookDirection = targetPosition - transform.position;
            if (lookDirection != Vector3.zero)
            {
                Quaternion lookRotation = Quaternion.LookRotation(lookDirection);
                transform.rotation = Quaternion.Lerp(transform.rotation, lookRotation, followSpeed * Time.deltaTime);
            }
        }
        
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
            if (target != null)
            {
                lastTargetPosition = target.position;
            }
        }
        
        public void FocusOnPosition(Vector3 position, float focusDistance = 0f)
        {
            if (focusDistance > 0f)
            {
                distance = focusDistance;
            }
            
            // Calculate rotation to look at position
            Vector3 direction = (transform.position - position).normalized;
            currentRotationY = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg;
            currentRotationX = Mathf.Asin(direction.y) * Mathf.Rad2Deg;
        }
        
        public void SetCameraMode(CameraMode mode)
        {
            switch (mode)
            {
                case CameraMode.Follow:
                    distance = 25f;
                    height = 15f;
                    currentRotationX = 30f;
                    break;
                    
                case CameraMode.Overhead:
                    distance = 30f;
                    height = 40f;
                    currentRotationX = 80f;
                    break;
                    
                case CameraMode.Close:
                    distance = 10f;
                    height = 5f;
                    currentRotationX = 10f;
                    break;
                    
                case CameraMode.Cinematic:
                    distance = 35f;
                    height = 20f;
                    currentRotationX = 25f;
                    break;
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (target != null)
            {
                // Draw camera target and range
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(target.position, 2f);
                
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(transform.position, target.position);
                
                // Draw distance range
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(target.position, minDistance);
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(target.position, maxDistance);
            }
        }
    }
    
    public enum CameraMode
    {
        Follow,
        Overhead,
        Close,
        Cinematic
    }
}
