using UnityEngine;
using System.Collections.Generic;

namespace StreetEmpire.Core
{
    /// <summary>
    /// Data structures for police system
    /// </summary>
    
    [System.Serializable]
    public class Investigation
    {
        public string id;
        public InvestigationType type;
        public float totalTime;
        public float remainingTime;
        public float evidenceLevel;
        public float startTime;
        public bool isCompleted;
        
        public float Progress => 1f - (remainingTime / totalTime);
        
        public string GetDescription()
        {
            switch (type)
            {
                case InvestigationType.DrugProduction:
                    return "Police are investigating drug manufacturing operations";
                case InvestigationType.MoneyLaundering:
                    return "Financial crimes unit is tracking suspicious transactions";
                case InvestigationType.Distribution:
                    return "Narcotics division is monitoring drug distribution networks";
                case InvestigationType.Corruption:
                    return "Internal affairs is investigating police corruption";
                default:
                    return "Police are conducting a general investigation";
            }
        }
    }
    
    [System.Serializable]
    public class Raid
    {
        public string id;
        public RaidType type;
        public float severity; // 0-1, affects consequences
        public float warningTime; // Seconds of warning before raid
        public float startTime;
        public bool isActive;
        public bool isCompleted;
        
        public string GetDescription()
        {
            switch (type)
            {
                case RaidType.LabRaid:
                    return "SWAT team is preparing to raid your production facilities";
                case RaidType.BusinessRaid:
                    return "Financial crimes unit is targeting your businesses";
                case RaidType.StreetSweep:
                    return "Police are conducting a street-level drug sweep";
                case RaidType.GeneralRaid:
                    return "Police are planning a coordinated operation";
                default:
                    return "Unknown police operation detected";
            }
        }
        
        public Color GetSeverityColor()
        {
            if (severity < 0.3f) return Color.yellow;
            if (severity < 0.7f) return Color.orange;
            return Color.red;
        }
    }
    
    [System.Serializable]
    public class CorruptOfficer
    {
        public string name;
        public float corruptionLevel; // 0-1, how much they help
        public float monthlyPayment;
        public float lastPayment;
        public bool isActive;
        public int rank; // Higher rank = more influence
        
        public bool NeedsPayment()
        {
            return Time.time - lastPayment >= 2592000f; // 30 days in seconds
        }
        
        public float GetInfluence()
        {
            return corruptionLevel * (1f + rank * 0.2f);
        }
        
        public string GetRankName()
        {
            switch (rank)
            {
                case 0: return "Officer";
                case 1: return "Detective";
                case 2: return "Sergeant";
                case 3: return "Lieutenant";
                case 4: return "Captain";
                default: return "Chief";
            }
        }
    }
    
    public enum InvestigationType
    {
        DrugProduction,
        MoneyLaundering,
        Distribution,
        Corruption,
        General
    }
    
    public enum RaidType
    {
        LabRaid,
        BusinessRaid,
        StreetSweep,
        GeneralRaid,
        AssetForfeiture
    }
    
    /// <summary>
    /// Evidence that can be found during raids
    /// </summary>
    [System.Serializable]
    public class Evidence
    {
        public string id;
        public EvidenceType type;
        public float incriminationLevel; // How damaging this evidence is
        public string description;
        public System.DateTime discoveryDate;
        public bool isRecovered; // Player can break into evidence room to recover
        
        public float GetPenalty()
        {
            return incriminationLevel * 1000f; // Base fine amount
        }
    }
    
    public enum EvidenceType
    {
        Drugs,
        Money,
        Equipment,
        Documents,
        DigitalEvidence,
        Weapons,
        ChemicalResidues
    }
    
    /// <summary>
    /// Police station data for evidence room break-ins
    /// </summary>
    [System.Serializable]
    public class PoliceStation
    {
        public string name;
        public Vector3 location;
        public SecurityLevel securityLevel;
        public List<Evidence> storedEvidence;
        public float lastBreakIn;
        
        public bool CanBreakIn()
        {
            // Can't break in too frequently
            return Time.time - lastBreakIn >= 604800f; // 7 days
        }
        
        public float GetBreakInDifficulty()
        {
            switch (securityLevel)
            {
                case SecurityLevel.Low: return 0.3f;
                case SecurityLevel.Medium: return 0.6f;
                case SecurityLevel.High: return 0.9f;
                default: return 1.0f;
            }
        }
    }
    
    public enum SecurityLevel
    {
        Low,
        Medium,
        High,
        Maximum
    }
    
    /// <summary>
    /// Parkour system for escaping police
    /// </summary>
    [System.Serializable]
    public class ParkourMove
    {
        public string name;
        public ParkourType type;
        public float difficulty; // 0-1
        public float energyCost;
        public float timeToComplete;
        public bool requiresSpecialSkill;
        
        public bool CanPerform(float playerSkill, float playerEnergy)
        {
            return playerSkill >= difficulty && playerEnergy >= energyCost;
        }
    }
    
    public enum ParkourType
    {
        Vault,
        Climb,
        Jump,
        Slide,
        Roll,
        WallRun
    }
    
    /// <summary>
    /// Chase sequence data
    /// </summary>
    [System.Serializable]
    public class ChaseSequence
    {
        public string id;
        public float duration;
        public float playerDistance; // Distance from police
        public float playerStamina;
        public List<ParkourMove> availableMoves;
        public bool isActive;
        
        public bool IsPlayerCaught()
        {
            return playerDistance <= 0f || playerStamina <= 0f;
        }
        
        public bool HasPlayerEscaped()
        {
            return playerDistance >= 100f; // Safe distance
        }
    }
}
